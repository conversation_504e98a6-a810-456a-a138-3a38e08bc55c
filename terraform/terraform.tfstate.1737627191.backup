{"version": 4, "terraform_version": "1.9.6", "serial": 5128, "lineage": "d4b61726-6321-29ce-fbd4-549463d150e1", "outputs": {"bootstrap_brokers_tls": {"value": "", "type": "string"}, "zookeeper_connect_string": {"value": "z-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_availability_zones", "name": "msk_azs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az4", "use1-az6", "use1-az1", "use1-az2", "use1-az3", "use1-az5"]}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-05b83679170a777c5", "associate_with_private_ip": null, "association_id": "eipassoc-0b79a82dfadb18846", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-05b83679170a777c5", "instance": "", "network_border_group": "us-east-1", "network_interface": "eni-085a5e4a106ee91e5", "private_dns": "ip-192-168-48-210.ec2.internal", "private_ip": "**************", "public_dns": "ec2-52-0-184-69.compute-1.amazonaws.com", "public_ip": "***********", "public_ipv4_pool": "amazon", "tags": {"Name": "nat-eks"}, "tags_all": {"Name": "nat-eks"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks-cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-cluster-keeps-eks-cluster", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2023-01-15T22:50:05Z", "description": "", "force_detach_policies": false, "id": "eks-cluster-keeps-eks-cluster", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"], "max_session_duration": 3600, "name": "eks-cluster-keeps-eks-cluster", "name_prefix": "", "path": "/", "permissions_boundary": null, "role_last_used": [{"last_used_date": "2025-01-23T09:24:56Z", "region": "us-east-1"}], "tags": {}, "tags_all": {}, "unique_id": "AROAXKTSYSM6FZN7SP6BR"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "nodes", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-node-group-nodes", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2023-01-15T22:50:05Z", "description": "", "force_detach_policies": false, "id": "eks-node-group-nodes", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"], "max_session_duration": 3600, "name": "eks-node-group-nodes", "name_prefix": "", "path": "/", "permissions_boundary": null, "role_last_used": [{"last_used_date": "2025-01-23T09:15:45Z", "region": "us-east-1"}], "tags": {}, "tags_all": {}, "unique_id": "AROAXKTSYSM6FZWGNBO6O"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-ec2-container-registry-read-only", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-group-nodes-20230115225008138400000002", "policy_arn": "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "role": "eks-node-group-nodes"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.nodes"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-cluster-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-cluster-keeps-eks-cluster-20230115225008113100000001", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "role": "eks-cluster-keeps-eks-cluster"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks-cluster"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-cni-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-group-nodes-20230115225008425600000004", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy", "role": "eks-node-group-nodes"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.nodes"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-worker-node-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-group-nodes-20230115225008335600000003", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "role": "eks-node-group-nodes"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.nodes"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:internet-gateway/igw-00cfcaa80d9a569b9", "id": "igw-00cfcaa80d9a569b9", "owner_id": "************", "tags": {"Name": "igw-eks"}, "tags_all": {"Name": "igw-eks"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_msk_cluster", "name": "msk_cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:kafka:us-east-1:************:cluster/msk-keeps/660fad9e-0867-4342-9210-b2f903b763c2-22", "bootstrap_brokers": "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092", "bootstrap_brokers_public_sasl_iam": "", "bootstrap_brokers_public_sasl_scram": "", "bootstrap_brokers_public_tls": "", "bootstrap_brokers_sasl_iam": "", "bootstrap_brokers_sasl_scram": "", "bootstrap_brokers_tls": "", "broker_node_group_info": [{"az_distribution": "DEFAULT", "client_subnets": ["subnet-02b8cc3207f27f0c1", "subnet-03b878a0064ad3ae7", "subnet-08ff81cd86517585e"], "connectivity_info": [{"public_access": [{"type": "DISABLED"}]}], "ebs_volume_size": 110, "instance_type": "kafka.t3.small", "security_groups": ["sg-0ff5d25bab06b2667"], "storage_info": [{"ebs_storage_info": [{"provisioned_throughput": [], "volume_size": 110}]}]}], "client_authentication": [], "cluster_name": "msk-keeps", "configuration_info": [{"arn": "arn:aws:kafka:us-east-1:************:configuration/keeps-kafka-minimal/f56f1c96-dd25-49dc-996d-d18f83bfdae5-18", "revision": 4}], "current_version": "KQBWZHOQDJZ1C", "encryption_info": [{"encryption_at_rest_kms_key_arn": "arn:aws:kms:us-east-1:************:key/f46d3d08-186c-49fa-9fc8-50be51bad8ca", "encryption_in_transit": [{"client_broker": "PLAINTEXT", "in_cluster": true}]}], "enhanced_monitoring": "DEFAULT", "id": "arn:aws:kafka:us-east-1:************:cluster/msk-keeps/660fad9e-0867-4342-9210-b2f903b763c2-22", "kafka_version": "3.7.x", "logging_info": [{"broker_logs": [{"cloudwatch_logs": [], "firehose": [], "s3": [{"bucket": "msk-keeps-logs-404ec4ef787bb029", "enabled": true, "prefix": "logs/msk-"}]}]}], "number_of_broker_nodes": 3, "open_monitoring": [{"prometheus": [{"jmx_exporter": [{"enabled_in_broker": true}], "node_exporter": [{"enabled_in_broker": true}]}]}], "storage_mode": "LOCAL", "tags": {}, "tags_all": {}, "timeouts": null, "zookeeper_connect_string": "z-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181", "zookeeper_connect_string_tls": "z-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2182,z-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2182,z-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2182"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo3MjAwMDAwMDAwMDAwLCJkZWxldGUiOjcyMDAwMDAwMDAwMDAsInVwZGF0ZSI6NzIwMDAwMDAwMDAwMH19", "dependencies": ["aws_s3_bucket.msk_bucket", "aws_security_group.msk_sg", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_id": "eipalloc-05b83679170a777c5", "association_id": "eipassoc-0b79a82dfadb18846", "connectivity_type": "public", "id": "nat-0059c413c49242a82", "network_interface_id": "eni-085a5e4a106ee91e5", "private_ip": "**************", "public_ip": "***********", "subnet_id": "subnet-04d84aa81e9ef855b", "tags": {"Name": "nat-gtw-eks"}, "tags_all": {"Name": "nat-gtw-eks"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_subnet.public-us-east-1a", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_network_acl", "name": "msk_nacl", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:network-acl/acl-0597bc34840b6f302", "egress": [{"action": "allow", "cidr_block": "***********/16", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "id": "acl-0597bc34840b6f302", "ingress": [{"action": "allow", "cidr_block": "***********/16", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "owner_id": "************", "subnet_ids": ["subnet-02b8cc3207f27f0c1", "subnet-03b878a0064ad3ae7", "subnet-08ff81cd86517585e"], "tags": {"Name": "msk-nacl"}, "tags_all": {"Name": "msk-nacl"}, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_network_acl_association", "name": "msk_nacl_assoc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "aclassoc-0907501fc7e9f9813", "network_acl_id": "acl-0597bc34840b6f302", "subnet_id": "subnet-02b8cc3207f27f0c1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 1, "schema_version": 0, "attributes": {"id": "aclassoc-06061e342ba838f84", "network_acl_id": "acl-0597bc34840b6f302", "subnet_id": "subnet-08ff81cd86517585e"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 2, "schema_version": 0, "attributes": {"id": "aclassoc-0547c24700bd0b882", "network_acl_id": "acl-0597bc34840b6f302", "subnet_id": "subnet-03b878a0064ad3ae7"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}]}, {"mode": "managed", "type": "aws_network_acl_rule", "name": "egress_all_to_peered_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cidr_block": "***********/16", "egress": true, "from_port": 0, "icmp_code": null, "icmp_type": null, "id": "nacl-685912358", "ipv6_cidr_block": "", "network_acl_id": "acl-0597bc34840b6f302", "protocol": "-1", "rule_action": "allow", "rule_number": 100, "to_port": 0}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_vpc.main", "aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_network_acl_rule", "name": "ingress_all_from_peered_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cidr_block": "***********/16", "egress": false, "from_port": 0, "icmp_code": null, "icmp_type": null, "id": "nacl-**********", "ipv6_cidr_block": "", "network_acl_id": "acl-0597bc34840b6f302", "protocol": "-1", "rule_action": "allow", "rule_number": 100, "to_port": 0}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_vpc.main", "aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_route", "name": "msk_to_eks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "***********/16", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-0241de867aa60a4823901788224", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-0241de867aa60a482", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-msk"]}]}, {"mode": "managed", "type": "aws_route", "name": "public_to_msk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "***********/22", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-02e1975533ca1177d2602932349", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-02e1975533ca1177d", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-08c179a52947b249a", "id": "rtb-08c179a52947b249a", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0059c413c49242a82", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, {"carrier_gateway_id": "", "cidr_block": "***********/22", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, {"carrier_gateway_id": "", "cidr_block": "***********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-01425ae53b3021c72"}, {"carrier_gateway_id": "", "cidr_block": "**********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-0788322b771487858"}], "tags": {"Name": "private"}, "tags_all": {"Name": "private"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-02e1975533ca1177d", "id": "rtb-02e1975533ca1177d", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-00cfcaa80d9a569b9", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, {"carrier_gateway_id": "", "cidr_block": "***********/22", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, {"carrier_gateway_id": "", "cidr_block": "***********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-01425ae53b3021c72"}, {"carrier_gateway_id": "", "cidr_block": "**********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-0788322b771487858"}], "tags": {"Name": "public"}, "tags_all": {"Name": "public"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_internet_gateway.igw", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0706a5ec5f16c1083", "route_table_id": "rtb-08c179a52947b249a", "subnet_id": "subnet-07580e2fc7b205504", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_route_table.private", "aws_subnet.private-us-east-1a", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0a7cee6fefee522a0", "route_table_id": "rtb-08c179a52947b249a", "subnet_id": "subnet-08068e3da6b79ab27", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_route_table.private", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-058b0d404c49d5e69", "route_table_id": "rtb-02e1975533ca1177d", "subnet_id": "subnet-04d84aa81e9ef855b", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-01eb4ccff17e4d864", "route_table_id": "rtb-02e1975533ca1177d", "subnet_id": "subnet-00684b0ce587ba6a1", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_subnet.public-us-east-1b", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "msk_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::msk-keeps-logs-404ec4ef787bb029", "bucket": "msk-keeps-logs-404ec4ef787bb029", "bucket_domain_name": "msk-keeps-logs-404ec4ef787bb029.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "msk-keeps-logs-404ec4ef787bb029.s3.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "70d9850736dda5b84e4f11ad8508126acf21eca55c3e1524ac19b24ef61a5256", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "msk-keeps-logs-404ec4ef787bb029", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Id\":\"AWSLogDeliveryWrite20150319\",\"Statement\":[{\"Action\":\"s3:PutObject\",\"Condition\":{\"ArnLike\":{\"aws:SourceArn\":\"arn:aws:logs:us-east-1:************:*\"},\"StringEquals\":{\"aws:SourceAccount\":\"************\",\"s3:x-amz-acl\":\"bucket-owner-full-control\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"delivery.logs.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::msk-keeps-logs-404ec4ef787bb029/logs/msk-/AWSLogs/************/*\",\"Sid\":\"AWSLogDeliveryWrite\"},{\"Action\":\"s3:GetBucketAcl\",\"Condition\":{\"ArnLike\":{\"aws:SourceArn\":\"arn:aws:logs:us-east-1:************:*\"},\"StringEquals\":{\"aws:SourceAccount\":\"************\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"delivery.logs.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::msk-keeps-logs-404ec4ef787bb029\",\"Sid\":\"AWSLogDeliveryAclCheck\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "msk_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0ff5d25bab06b2667", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0ff5d25bab06b2667", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "Allow Kafka traffic", "from_port": 9092, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 9092}], "name": "terraform-20240211222203966400000001", "name_prefix": "terraform-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Name": "msk-sg"}, "tags_all": {"Name": "msk-sg"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "msk_subnet", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-02b8cc3207f27f0c1", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-02b8cc3207f27f0c1", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "msk-subnet-0"}, "tags_all": {"Name": "msk-subnet-0"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-08ff81cd86517585e", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-08ff81cd86517585e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "msk-subnet-1"}, "tags_all": {"Name": "msk-subnet-1"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 2, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-03b878a0064ad3ae7", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1c", "availability_zone_id": "use1-az1", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-03b878a0064ad3ae7", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "msk-subnet-2"}, "tags_all": {"Name": "msk-subnet-2"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-07580e2fc7b205504", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-07580e2fc7b205504", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "private-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Name": "private-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-08068e3da6b79ab27", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-08068e3da6b79ab27", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "private-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Name": "private-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-04d84aa81e9ef855b", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-04d84aa81e9ef855b", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-00684b0ce587ba6a1", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-00684b0ce587ba6a1", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-us-east-1f", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-03e0588ec4f3ebf43", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1f", "availability_zone_id": "use1-az5", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-03e0588ec4f3ebf43", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-us-east-1f", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-us-east-1f", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-05621d5af0661cb6d", "assign_generated_ipv6_cidr_block": false, "cidr_block": "***********/16", "default_network_acl_id": "acl-02c1d5f706dd8a655", "default_route_table_id": "rtb-0afab0015fb750c43", "default_security_group_id": "sg-0c331b38c00c91912", "dhcp_options_id": "dopt-c57962a7", "enable_classiclink": false, "enable_classiclink_dns_support": false, "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-05621d5af0661cb6d", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0afab0015fb750c43", "owner_id": "************", "tags": {"Name": "main-eks-vpc"}, "tags_all": {"Name": "main-eks-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_vpc", "name": "msk_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-065bdb039b06329cd", "assign_generated_ipv6_cidr_block": false, "cidr_block": "***********/22", "default_network_acl_id": "acl-04d18bb91f1bd0072", "default_route_table_id": "rtb-0241de867aa60a482", "default_security_group_id": "sg-037c3f3bbeb12f38b", "dhcp_options_id": "dopt-c57962a7", "enable_classiclink": false, "enable_classiclink_dns_support": false, "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-065bdb039b06329cd", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0241de867aa60a482", "owner_id": "************", "tags": {"Name": "msk-vpc"}, "tags_all": {"Name": "msk-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_vpc_peering_connection", "name": "eks-to-keeps", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"accept_status": "active", "accepter": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "auto_accept": true, "id": "pcx-0788322b771487858", "peer_owner_id": "************", "peer_region": "us-east-1", "peer_vpc_id": "vpc-e7e87b82", "requester": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "tags": {"Name": "VPC Peering between EKS and Keeps"}, "tags_all": {"Name": "VPC Peering between EKS and Keeps"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMCwiZGVsZXRlIjo2MDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc_peering_connection", "name": "eks-to-msk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"accept_status": "active", "accepter": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "auto_accept": true, "id": "pcx-023ace3ce04b9b77d", "peer_owner_id": "************", "peer_region": "us-east-1", "peer_vpc_id": "vpc-065bdb039b06329cd", "requester": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "tags": {"Name": "VPC Peering between EKS and MSK"}, "tags_all": {"Name": "VPC Peering between EKS and MSK"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMCwiZGVsZXRlIjo2MDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main", "aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_vpc_peering_connection", "name": "eks-to-rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"accept_status": "active", "accepter": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "auto_accept": true, "id": "pcx-01425ae53b3021c72", "peer_owner_id": "************", "peer_region": "us-east-1", "peer_vpc_id": "vpc-068d31db956dfe39a", "requester": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "tags": {"Name": "VPC Peering between EKS and RDS"}, "tags_all": {"Name": "VPC Peering between EKS and RDS"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMCwiZGVsZXRlIjo2MDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "helm_release", "name": "metrics_server", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "metrics-server", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "metrics-server", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "0.7.2", "chart": "metrics-server", "first_deployed": **********, "last_deployed": **********, "name": "metrics-server", "namespace": "kube-system", "notes": "***********************************************************************\n* Metrics Server                                                      *\n***********************************************************************\n  Chart version: 3.12.2\n  App version:   0.7.2\n  Image tag:     registry.k8s.io/metrics-server/metrics-server:v0.7.2\n***********************************************************************\n", "revision": 1, "values": "{\"args\":[\"--kubelet-insecure-tls\",\"--kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname\"],\"resources\":{\"limits\":{\"cpu\":\"250m\",\"memory\":\"256Mi\"},\"requests\":{\"cpu\":\"100m\",\"memory\":\"128Mi\"}}}", "version": "3.12.2"}], "name": "metrics-server", "namespace": "kube-system", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": "https://kubernetes-sigs.github.io/metrics-server/", "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "upgrade_install": null, "values": ["args:\n  - --kubelet-insecure-tls\n  - --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname\nresources:\n  requests:\n    cpu: 100m\n    memory: 128Mi\n  limits:\n    cpu: 250m\n    memory: 256Mi\n"], "verify": false, "version": "3.12.2", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "helm_release", "name": "nginx-controller", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "https://github.com/kubernetes/ingress-nginx/releases/download/helm-chart-4.9.1/ingress-nginx-4.9.1.tgz", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "nginx-controller", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "1.9.6", "chart": "ingress-nginx", "first_deployed": **********, "last_deployed": **********, "name": "nginx-controller", "namespace": "default", "notes": "The ingress-nginx controller has been installed.\nIt may take a few minutes for the load balancer IP to be available.\nYou can watch the status by running 'kubectl get service --namespace default nginx-controller-ingress-nginx-controller --output wide --watch'\n\nAn example Ingress that makes use of the controller:\n  apiVersion: networking.k8s.io/v1\n  kind: Ingress\n  metadata:\n    name: example\n    namespace: foo\n  spec:\n    ingressClassName: nginx\n    rules:\n      - host: www.example.com\n        http:\n          paths:\n            - pathType: Prefix\n              backend:\n                service:\n                  name: exampleService\n                  port:\n                    number: 80\n              path: /\n    # This section is only required if TLS is to be enabled for the Ingress\n    tls:\n      - hosts:\n        - www.example.com\n        secretName: example-tls\n\nIf TLS is enabled for the Ingress, a Secret containing the certificate and key must also be provided:\n\n  apiVersion: v1\n  kind: Secret\n  metadata:\n    name: example-tls\n    namespace: foo\n  data:\n    tls.crt: <base64 encoded cert>\n    tls.key: <base64 encoded key>\n  type: kubernetes.io/tls\n", "revision": 1, "values": "{\"controller\":{\"autoscaling\":{\"enabled\":\"true\",\"maxReplicas\":\"5\",\"minReplicas\":\"3\"},\"service\":{\"annotations\":{\"service.beta.kubernetes.io/aws-load-balancer-backend-protocol\":\"http\",\"service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout\":\"60\",\"service.beta.kubernetes.io/aws-load-balancer-ssl-cert\":\"arn:aws:acm:us-east-1:************:certificate/f1bc42e4-8e77-4679-abda-626635d6017d\",\"service.beta.kubernetes.io/aws-load-balancer-ssl-ports\":\"https\",\"service.beta.kubernetes.io/aws-load-balancer-type\":\"nlb\"},\"targetPorts\":{\"http\":\"http\",\"https\":\"http\"}}}}", "version": "4.9.1"}], "name": "nginx-controller", "namespace": "default", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": null, "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [{"name": "controller.autoscaling.enabled", "type": "string", "value": "true"}, {"name": "controller.autoscaling.maxReplicas", "type": "string", "value": "5"}, {"name": "controller.autoscaling.minReplicas", "type": "string", "value": "3"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-backend-protocol", "type": "string", "value": "http"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-connection-idle-timeout", "type": "string", "value": "60"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-cert", "type": "string", "value": "arn:aws:acm:us-east-1:************:certificate/f1bc42e4-8e77-4679-abda-626635d6017d"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-ports", "type": "string", "value": "https"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type", "type": "string", "value": "nlb"}, {"name": "controller.service.targetPorts.http", "type": "string", "value": "http"}, {"name": "controller.service.targetPorts.https", "type": "string", "value": "http"}], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "upgrade_install": null, "values": null, "verify": false, "version": "4.9.1", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "production", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "production", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "production", "resource_version": "1747548", "uid": "526c9d47-9c0c-4015-8c87-0f2ee162681c"}], "timeouts": null, "wait_for_default_service_account": null}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "stage", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "stage", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "stage", "resource_version": "8559", "uid": "63cdbb95-7314-4b54-b591-70bf88a7f747"}], "timeouts": null, "wait_for_default_service_account": null}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "random_id", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 0, "attributes": {"b64_std": "QE7E73h7sCk=", "b64_url": "QE7E73h7sCk", "byte_length": 8, "dec": "4633857599408418857", "hex": "404ec4ef787bb029", "id": "QE7E73h7sCk", "keepers": null, "prefix": null}, "sensitive_attributes": []}]}], "check_results": null}