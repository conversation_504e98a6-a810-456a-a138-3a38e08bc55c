terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.39"
    }

    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }

    local = ">=2.1.0"
  }
}

provider "aws" {
  region = "us-east-1"
}
provider "aws" {
  alias  = "west"
  region = "us-west-2"
}

variable "cluster_name" {
  default = "keeps-eks-cluster"
}

variable "cluster_version" {
  default = "1.32"
}

variable "aws_region" {
  default = "us-east-1"
}