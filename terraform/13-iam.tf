###########################
# PRODUCTION
###########################
module "kontent_microservice_prod" {
  source      = "./iam"
  user_name   = "kontent-microservice-prod"
  policy_path = "policies/kontent-microservice-prod-policy.json"
}

module "media_convert_microservice_prod" {
  source      = "./iam"
  user_name   = "media-convert-microservice-prod"
  policy_path = "policies/media-convert-microservice-prod-policy.json"
}


###########################
# STAGE
###########################
module "kontent_microservice_stage" {
  source      = "./iam"
  user_name   = "kontent-microservice-stage"
  policy_path = "policies/kontent-microservice-stage-policy.json"
}
module "media_convert_microservice_stage" {
  source      = "./iam"
  user_name   = "media-convert-microservice-stage"
  policy_path = "policies/media-convert-microservice-stage-policy.json"
}
