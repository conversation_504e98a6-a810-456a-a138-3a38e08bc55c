resource "helm_release" "nginx-controller" {
  name  = "nginx-controller"
  chart = "https://github.com/kubernetes/ingress-nginx/releases/download/helm-chart-4.12.2/ingress-nginx-4.12.2.tgz"

  set {
    name  = "controller.allowSnippetAnnotations"
    value = "true"
  }

  set {
    name  = "controller.config.annotations-risk-level"
    value = "Critical"
  }

  set {
    name  = "controller.ingressClassResource.name"
    value = "nginx"
    type  = "string"
  }

  set {
    name  = "controller.service.targetPorts.http"
    value = "http"
    type  = "string"
  }

  set {
    name  = "controller.service.targetPorts.https"
    value = "http"
    type  = "string"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-backend-protocol"
    value = "http"
    type  = "string"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-ports"
    value = "https"
    type  = "string"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-connection-idle-timeout"
    value = "60"
    type  = "string"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-cert"
    value = "arn:aws:acm:us-east-1:503825601340:certificate/f1bc42e4-8e77-4679-abda-626635d6017d"
    type  = "string"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type"
    value = "nlb"
    type  = "string"
  }

  set {
    name  = "controller.autoscaling.enabled"
    value = "true"
  }
  set {
    name  = "controller.autoscaling.minReplicas"
    value = "3"
    type  = "string"
  }

  set {
    name  = "controller.autoscaling.maxReplicas"
    value = "5"
    type  = "string"
  }
}
