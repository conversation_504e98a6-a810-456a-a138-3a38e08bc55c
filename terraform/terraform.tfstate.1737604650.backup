{"version": 4, "terraform_version": "1.9.6", "serial": 5096, "lineage": "d4b61726-6321-29ce-fbd4-549463d150e1", "outputs": {"bootstrap_brokers_tls": {"value": "", "type": "string"}, "kubeconfig-message": {"value": "Execute the following command 'cp kubeconfig ~/.kube/config' and run 'terraform apply' again", "type": "string"}, "zookeeper_connect_string": {"value": "z-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_availability_zones", "name": "msk_azs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az4", "use1-az6", "use1-az1", "use1-az2", "use1-az3", "use1-az5"]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "csi", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "395554895", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRoleWithWebIdentity\",\n      \"Principal\": {\n        \"Federated\": \"arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E\"\n      },\n      \"Condition\": {\n        \"StringEquals\": {\n          \"oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E:sub\": \"system:serviceaccount:kube-system:ebs-csi-controller-sa\"\n        }\n      }\n    }\n  ]\n}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:AssumeRoleWithWebIdentity"], "condition": [{"test": "StringEquals", "values": ["system:serviceaccount:kube-system:ebs-csi-controller-sa"], "variable": "oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E:sub"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E"], "type": "Federated"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "tls_certificate", "name": "eks", "provider": "provider[\"registry.terraform.io/hashicorp/tls\"]", "instances": [{"schema_version": 0, "attributes": {"certificates": [{"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEdTCCA12gAwIBAgIJAKcOSkw0grd/MA0GCSqGSIb3DQEBCwUAMGgxCzAJBgNV\nBAYTAlVTMSUwIwYDVQQKExxTdGFyZmllbGQgVGVjaG5vbG9naWVzLCBJbmMuMTIw\nMAYDVQQLEylTdGFyZmllbGQgQ2xhc3MgMiBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0\neTAeFw0wOTA5MDIwMDAwMDBaFw0zNDA2MjgxNzM5MTZaMIGYMQswCQYDVQQGEwJV\nUzEQMA4GA1UECBMHQXJpem9uYTETMBEGA1UEBxMKU2NvdHRzZGFsZTElMCMGA1UE\nChMcU3RhcmZpZWxkIFRlY2hub2xvZ2llcywgSW5jLjE7MDkGA1UEAxMyU3RhcmZp\nZWxkIFNlcnZpY2VzIFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IC0gRzIwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDVDDrEKvlO4vW+GZdfjohTsR8/\ny8+fIBNtKTrID30892t2OGPZNmCom15cAICyL1l/9of5JUOG52kbUpqQ4XHj2C0N\nTm/2yEnZtvMaVq4rtnQU68/7JuMauh2WLmo7WJSJR1b/JaCTcFOD2oR0FMNnngRo\nOt+OQFodSk7PQ5E751bWAHDLUu57fa4657wx+UX2wmDPE1kCK4DMNEffud6QZW0C\nzyyRpqbn3oUYSXxmTqM6bam17jQuug0DuDPfR+uxa40l2ZvOgdFFRjKWcIfeAg5J\nQ4W2bHO7ZOphQazJ1FTfhy/HIrImzJ9ZVGif/L4qL8RVHHVAYBeFAlU5i38FAgMB\nAAGjgfAwge0wDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0O\nBBYEFJxfAN+qAdcwKziIorhtSpzyEZGDMB8GA1UdIwQYMBaAFL9ft9HO3R+G9FtV\nrNzXEMIOqYjnME8GCCsGAQUFBwEBBEMwQTAcBggrBgEFBQcwAYYQaHR0cDovL28u\nc3MyLnVzLzAhBggrBgEFBQcwAoYVaHR0cDovL3guc3MyLnVzL3guY2VyMCYGA1Ud\nHwQfMB0wG6AZoBeGFWh0dHA6Ly9zLnNzMi51cy9yLmNybDARBgNVHSAECjAIMAYG\nBFUdIAAwDQYJKoZIhvcNAQELBQADggEBACMd44pXyn3pF3lM8R5V/cxTbj5HD9/G\nVfKyBDbtgB9TxF00KGu+x1X8Z+rLP3+QsjPNG1gQggL4+C/1E2DUBc7xgQjB3ad1\nl08YuW3e95ORCLp+QCztweq7dp4zBncdDQh/U90bZKuCJ/Fp1U1ervShw3WnWEQt\n8jxwmKy6abaVd38PMV4s/KCHOkdp8Hlf9BRUpJVeEXgSYCfOn8J3/yNTd126/+pZ\n59vPr5KW7ySaNRB6nJHGDn2Z9j8Z3/VyVOEVqQdZe4O/Ui5GjLIAZHYcSNPYeehu\nVsyuLAOQ1xk4meTKCRlb/weWsKh/NEnfVqn3sF/tM+2MR7cwA130A4w=\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "OU=Starfield Class 2 Certification Authority,O=Starfield Technologies\\, Inc.,C=US", "not_after": "2034-06-28T17:39:16Z", "not_before": "2009-09-02T00:00:00Z", "public_key_algorithm": "RSA", "serial_number": "12037640545166866303", "sha1_fingerprint": "9e99a48a9960b14926bb7f3b02e22da2b0ab7280", "signature_algorithm": "SHA256-RSA", "subject": "CN=Starfield Services Root Certificate Authority - G2,O=Starfield Technologies\\, Inc.,L=Scottsdale,ST=Arizona,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEkjCCA3qgAwIBAgITBn+USionzfP6wq4rAfkI7rnExjANBgkqhkiG9w0BAQsF\nADCBmDELMAkGA1UEBhMCVVMxEDAOBgNVBAgTB0FyaXpvbmExEzARBgNVBAcTClNj\nb3R0c2RhbGUxJTAjBgNVBAoTHFN0YXJmaWVsZCBUZWNobm9sb2dpZXMsIEluYy4x\nOzA5BgNVBAMTMlN0YXJmaWVsZCBTZXJ2aWNlcyBSb290IENlcnRpZmljYXRlIEF1\ndGhvcml0eSAtIEcyMB4XDTE1MDUyNTEyMDAwMFoXDTM3MTIzMTAxMDAwMFowOTEL\nMAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv\nb3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj\nca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM\n9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw\nIFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6\nVOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L\n93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm\njgSubJrIqg0CAwEAAaOCATEwggEtMA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/\nBAQDAgGGMB0GA1UdDgQWBBSEGMyFNOy8DJSULghZnMeyEE4KCDAfBgNVHSMEGDAW\ngBScXwDfqgHXMCs4iKK4bUqc8hGRgzB4BggrBgEFBQcBAQRsMGowLgYIKwYBBQUH\nMAGGImh0dHA6Ly9vY3NwLnJvb3RnMi5hbWF6b250cnVzdC5jb20wOAYIKwYBBQUH\nMAKGLGh0dHA6Ly9jcnQucm9vdGcyLmFtYXpvbnRydXN0LmNvbS9yb290ZzIuY2Vy\nMD0GA1UdHwQ2MDQwMqAwoC6GLGh0dHA6Ly9jcmwucm9vdGcyLmFtYXpvbnRydXN0\nLmNvbS9yb290ZzIuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQsF\nAAOCAQEAYjdCXLwQtT6LLOkMm2xF4gcAevnFWAu5CIw+7bMlPLVvUOTNNWqnkzSW\nMiGpSESrnO09tKpzbeR/FoCJbM8oAxiDR3mjEH4wW6w7sGDgd9QIpuEdfF7Au/ma\neyKdpwAJfqxGF4PcnCZXmTA5YpaP7dreqsXMGz7KQ2hsVxa81Q4gLv7/wmpdLqBK\nbRRYh5TmOTFffHPLkIhqhBGWJ6bt2YFGpn6jcgAKUj6DiAdjd4lpFw85hdKrCEVN\n0FE6/V1dN2RMfjCyVSRCnTawXZwXgWHxyvkQAiSr6w10kY17RSlQOYiypok1JR4U\nakcjMS9cmvqtmg5iUaQqqcT5NJ0hGA==\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "CN=Starfield Services Root Certificate Authority - G2,O=Starfield Technologies\\, Inc.,L=Scottsdale,ST=Arizona,C=US", "not_after": "2037-12-31T01:00:00Z", "not_before": "2015-05-25T12:00:00Z", "public_key_algorithm": "RSA", "serial_number": "144918191876577076464031512351042010504348870", "sha1_fingerprint": "06b25927c42a721631c1efd9431e648fa62e1e39", "signature_algorithm": "SHA256-RSA", "subject": "CN=Amazon Root CA 1,O=Amazon,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEXjCCA0agAwIBAgITB3MSSkvL1E7HtTvq8ZSELToPoTANBgkqhkiG9w0BAQsF\nADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6\nb24gUm9vdCBDQSAxMB4XDTIyMDgyMzIyMjUzMFoXDTMwMDgyMzIyMjUzMFowPDEL\nMAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEcMBoGA1UEAxMTQW1hem9uIFJT\nQSAyMDQ4IE0wMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALtDGMZa\nqHneKei1by6+pUPPLljTB143Si6VpEWPc6mSkFhZb/6qrkZyoHlQLbDYnI2D7hD0\nsdzEqfnuAjIsuXQLG3A8TvX6V3oFNBFVe8NlLJHvBseKY88saLwufxkZVwk74g4n\nWlNMXzla9Y5F3wwRHwMVH443xGz6UtGSZSqQ94eFx5X7Tlqt8whi8qCaKdZ5rNak\n+r9nUThOeClqFd4oXych//Rc7Y0eX1KNWHYSI1Nk31mYgiK3JvH063g+K9tHA63Z\neTgKgndlh+WI+zv7i44HepRZjA1FYwYZ9Vv/9UkC5Yz8/yU65fgjaE+wVHM4e/Yy\nC2osrPWE7gJ+dXMCAwEAAaOCAVowggFWMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYD\nVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjAdBgNV\nHQ4EFgQUwDFSzVpQw4J8dHHOy+mc+XrrguIwHwYDVR0jBBgwFoAUhBjMhTTsvAyU\nlC4IWZzHshBOCggwewYIKwYBBQUHAQEEbzBtMC8GCCsGAQUFBzABhiNodHRwOi8v\nb2NzcC5yb290Y2ExLmFtYXpvbnRydXN0LmNvbTA6BggrBgEFBQcwAoYuaHR0cDov\nL2NydC5yb290Y2ExLmFtYXpvbnRydXN0LmNvbS9yb290Y2ExLmNlcjA/BgNVHR8E\nODA2MDSgMqAwhi5odHRwOi8vY3JsLnJvb3RjYTEuYW1hem9udHJ1c3QuY29tL3Jv\nb3RjYTEuY3JsMBMGA1UdIAQMMAowCAYGZ4EMAQIBMA0GCSqGSIb3DQEBCwUAA4IB\nAQAtTi6Fs0Azfi+iwm7jrz+CSxHH+uHl7Law3MQSXVtR8RV53PtR6r/6gNpqlzdo\nZq4FKbADi1v9Bun8RY8D51uedRfjsbeodizeBB8nXmeyD33Ep7VATj4ozcd31YFV\nfgRhvTSxNrrTlNpWkUk0m3BMPv8sg381HhA6uEYokE5q9uws/3YkKqRiEz3TsaWm\nJqIRZhMbgAfp7O7FUwFIb7UIspogZSKxPIWJpxiPo3TcBambbVtQOcNRWz5qCQdD\nslI2yayq0n2TXoHyNCLEH8rpsJRVILFsg0jc7BaFrMnF462+ajSehgj12IidNeRN\n4zl+EoNaWdpnWndvSpAEkq2P\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "CN=Amazon Root CA 1,O=Amazon,C=US", "not_after": "2030-08-23T22:25:30Z", "not_before": "2022-08-23T22:25:30Z", "public_key_algorithm": "RSA", "serial_number": "166129353110899469622597955040406457904926625", "sha1_fingerprint": "414a2060b738c635cc7fc243e052615592830c53", "signature_algorithm": "SHA256-RSA", "subject": "CN=Amazon RSA 2048 M02,O=Amazon,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----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\nqQ3LlAF6Op6n7JLa2rlXkf6LienZDVlSuQ==\n-----END CERTIFICATE-----\n", "is_ca": false, "issuer": "CN=Amazon RSA 2048 M02,O=Amazon,C=US", "not_after": "2025-04-26T23:59:59Z", "not_before": "2024-03-28T00:00:00Z", "public_key_algorithm": "RSA", "serial_number": "11397038078078022747295509550808604104", "sha1_fingerprint": "9451ad2b53c7f41fab22886cc07d482085336561", "signature_algorithm": "SHA256-RSA", "subject": "CN=*.eks.us-east-1.amazonaws.com", "version": 3}], "content": null, "id": "99d41e43229a4cdaf4141f3e8310e6d95c31dab9", "url": "https://oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E", "verify_chain": true}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-05b83679170a777c5", "associate_with_private_ip": null, "association_id": "eipassoc-0b79a82dfadb18846", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-05b83679170a777c5", "instance": "", "network_border_group": "us-east-1", "network_interface": "eni-085a5e4a106ee91e5", "private_dns": "ip-192-168-48-210.ec2.internal", "private_ip": "**************", "public_dns": "ec2-52-0-184-69.compute-1.amazonaws.com", "public_ip": "***********", "public_ipv4_pool": "amazon", "tags": {"Name": "nat-eks"}, "tags_all": {"Name": "nat-eks"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_eks_addon", "name": "csi_driver", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"addon_name": "aws-ebs-csi-driver", "addon_version": "v1.37.0-eksbuild.1", "arn": "arn:aws:eks:us-east-1:************:addon/keeps-eks-cluster/aws-ebs-csi-driver/98c592c6-aad5-9446-486f-0a25a7f7c2b4", "cluster_name": "keeps-eks-cluster", "configuration_values": "", "created_at": "2023-10-12T20:21:41Z", "id": "keeps-eks-cluster:aws-ebs-csi-driver", "modified_at": "2025-01-23T03:22:46Z", "preserve": null, "resolve_conflicts": null, "service_account_role_arn": "arn:aws:iam::************:role/eks-ebs-csi-driver", "tags": {}, "tags_all": {}, "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_openid_connect_provider.eks", "aws_iam_role.eks-cluster", "aws_iam_role.eks_ebs_csi_driver", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main", "data.aws_iam_policy_document.csi", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_eks_cluster", "name": "cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:eks:us-east-1:************:cluster/keeps-eks-cluster", "certificate_authority": [{"data": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1ERXhOVEl5TlRZek5Gb1hEVE16TURFeE1qSXlOVFl6TkZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBT01XCjJ2R2tkVndsR296d0FXcUMyc1I1Ri9tejhqS2NZOStrUmVtd1lsYzRPK0cvS3BCZ3ZwTFNtWXpUemJ6QzJJZ2gKbHhTUmhMdXZjdHRUdWhER25LQXJseFFjT2VROGxTaW5WekUySGc1RnBMRFFuY3RtNGxEdkYxUVcwY0lUak5HZgozZFVqZnRic2JmTEtIT3pSdGlWbWcrWlllbnFhWVdyWUhsaHRaSWxHa2FCb014dkRjbjdSOGxhY1ROR3NlUHRDCkxza2c0akhycTFpTVNvR3AxV3Vob3F3TVp0VnQ2Z0swTWpkMVRjSFVORmEwZXJLT3lRWjNWbXBidzhwT0pyNUwKaFhOSWlEYVNSc0tIUlJFV1hERHVoM09JYnMzNjQyMml0VS9HZVlKN3NXS0NvcEljMVBHRDlZd1RlbGRuTkEvTQpPNWZTSDVLZE1UWGNac1BoVFNjQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZDSEZLK0dsbFRVbE43WXRoay9WKzFBN3pVOThNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSFA2clFNbnlxTjNleXU1bmtLZApYRDZheThHRUxJNkRuOHVLc0h6Y3ByQjk3OWNnUnhuTW1lcllvVVc3RzMra3JTcXVSbHk5LzJUb2pFWjVNNklOClJFeS84dDYwTkF3aElESDlWTUJVKzV1R0tSMGw0VTBwTnZxTFRkYzlYTXdnKzN2VDNpVjMra3dJZ21nMnpKMDYKZ3JXS2NMQVE4Vkp1dENkbm9rQkFQTVdxazJyUXk2QWY0NkJNM2luV25NRHREakY1ZXZPTVV6RkhqUFgrUThxUgpFWmw4b25mdmZYZ1E3b2xVeG1FMjdGc0dOenF5dHlNOTJrazdmT2MyVXBCdzVmL3AwUG15VndGd2Q1WWQvRWpXCnJUejZPRlJqei85SVNDb3lxNkZjTG1iaC9vRmE4c2JXK0s2cUtoLzhQanVSY2NkYkdRcnEvVmRKMm5FWHdtL08KWFNFPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}], "cluster_id": null, "created_at": "2023-01-15 22:50:32.807 +0000 UTC", "enabled_cluster_log_types": [], "encryption_config": [], "endpoint": "https://A06CAB855832EAF9D15509A904042B4E.gr7.us-east-1.eks.amazonaws.com", "id": "keeps-eks-cluster", "identity": [{"oidc": [{"issuer": "https://oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E"}]}], "kubernetes_network_config": [{"ip_family": "ipv4", "service_ipv4_cidr": "**********/16", "service_ipv6_cidr": ""}], "name": "keeps-eks-cluster", "outpost_config": [], "platform_version": "eks.16", "role_arn": "arn:aws:iam::************:role/eks-cluster-keeps-eks-cluster", "status": "ACTIVE", "tags": {}, "tags_all": {}, "timeouts": null, "version": "1.31", "vpc_config": [{"cluster_security_group_id": "sg-0dbff7a85ecd56565", "endpoint_private_access": false, "endpoint_public_access": true, "public_access_cidrs": ["0.0.0.0/0"], "security_group_ids": [], "subnet_ids": ["subnet-00684b0ce587ba6a1", "subnet-04d84aa81e9ef855b", "subnet-07580e2fc7b205504", "subnet-08068e3da6b79ab27"], "vpc_id": "vpc-05621d5af0661cb6d"}]}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_eks_node_group", "name": "private-nodes", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"status": "tainted", "schema_version": 0, "attributes": {"ami_type": null, "arn": null, "capacity_type": "ON_DEMAND", "cluster_name": "keeps-eks-cluster", "disk_size": null, "force_update_version": null, "id": "keeps-eks-cluster:private-nodes", "instance_types": ["t3a.medium"], "labels": {"role": "general"}, "launch_template": [], "node_group_name": "private-nodes", "node_group_name_prefix": null, "node_role_arn": "arn:aws:iam::************:role/eks-node-group-nodes", "release_version": null, "remote_access": [], "resources": null, "scaling_config": [{"desired_size": 16, "max_size": 24, "min_size": 10}], "status": null, "subnet_ids": ["subnet-07580e2fc7b205504", "subnet-08068e3da6b79ab27"], "tags": null, "tags_all": null, "taint": [], "timeouts": null, "update_config": [{"max_unavailable": 1, "max_unavailable_percentage": 0}], "version": "1.31"}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_role.eks-cluster", "aws_iam_role.nodes", "aws_iam_role_policy_attachment.amazon-ec2-container-registry-read-only", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_iam_role_policy_attachment.amazon-eks-cni-policy", "aws_iam_role_policy_attachment.amazon-eks-worker-node-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_iam_openid_connect_provider", "name": "eks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E", "client_id_list": ["sts.amazonaws.com"], "id": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E", "tags": {}, "tags_all": {}, "thumbprint_list": ["9e99a48a9960b14926bb7f3b02e22da2b0ab7280"], "url": "oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks-cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-cluster-keeps-eks-cluster", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2023-01-15T22:50:05Z", "description": "", "force_detach_policies": false, "id": "eks-cluster-keeps-eks-cluster", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"], "max_session_duration": 3600, "name": "eks-cluster-keeps-eks-cluster", "name_prefix": "", "path": "/", "permissions_boundary": null, "role_last_used": [{"last_used_date": "2025-01-23T03:23:56Z", "region": "us-east-1"}], "tags": {}, "tags_all": {}, "unique_id": "AROAXKTSYSM6FZN7SP6BR"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks_ebs_csi_driver", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-ebs-csi-driver", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRoleWithWebIdentity\",\"Condition\":{\"StringEquals\":{\"oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E:sub\":\"system:serviceaccount:kube-system:ebs-csi-controller-sa\"}},\"Effect\":\"Allow\",\"Principal\":{\"Federated\":\"arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/A06CAB855832EAF9D15509A904042B4E\"},\"Sid\":\"\"}],\"Version\":\"2012-10-17\"}", "create_date": "2023-10-12T20:21:34Z", "description": "", "force_detach_policies": false, "id": "eks-ebs-csi-driver", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"], "max_session_duration": 3600, "name": "eks-ebs-csi-driver", "name_prefix": "", "path": "/", "permissions_boundary": null, "role_last_used": [{"last_used_date": "2025-01-22T22:00:21Z", "region": "us-east-1"}], "tags": {}, "tags_all": {}, "unique_id": "AROAXKTSYSM6EPDICMNON"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_openid_connect_provider.eks", "aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main", "data.aws_iam_policy_document.csi", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "nodes", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-node-group-nodes", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2023-01-15T22:50:05Z", "description": "", "force_detach_policies": false, "id": "eks-node-group-nodes", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"], "max_session_duration": 3600, "name": "eks-node-group-nodes", "name_prefix": "", "path": "/", "permissions_boundary": null, "role_last_used": [{"last_used_date": "2025-01-23T03:23:20Z", "region": "us-east-1"}], "tags": {}, "tags_all": {}, "unique_id": "AROAXKTSYSM6FZWGNBO6O"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-ec2-container-registry-read-only", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-group-nodes-20230115225008138400000002", "policy_arn": "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "role": "eks-node-group-nodes"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.nodes"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-cluster-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-cluster-keeps-eks-cluster-20230115225008113100000001", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "role": "eks-cluster-keeps-eks-cluster"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks-cluster"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-cni-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-group-nodes-20230115225008425600000004", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy", "role": "eks-node-group-nodes"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.nodes"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-worker-node-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-group-nodes-20230115225008335600000003", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "role": "eks-node-group-nodes"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.nodes"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon_ebs_csi_driver", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-ebs-csi-driver-20231012202125238900000002", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy", "role": "eks-ebs-csi-driver"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_openid_connect_provider.eks", "aws_iam_role.eks-cluster", "aws_iam_role.eks_ebs_csi_driver", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main", "data.aws_iam_policy_document.csi", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:internet-gateway/igw-00cfcaa80d9a569b9", "id": "igw-00cfcaa80d9a569b9", "owner_id": "************", "tags": {"Name": "igw-eks"}, "tags_all": {"Name": "igw-eks"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_msk_cluster", "name": "msk_cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:kafka:us-east-1:************:cluster/msk-keeps/660fad9e-0867-4342-9210-b2f903b763c2-22", "bootstrap_brokers": "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092", "bootstrap_brokers_public_sasl_iam": "", "bootstrap_brokers_public_sasl_scram": "", "bootstrap_brokers_public_tls": "", "bootstrap_brokers_sasl_iam": "", "bootstrap_brokers_sasl_scram": "", "bootstrap_brokers_tls": "", "broker_node_group_info": [{"az_distribution": "DEFAULT", "client_subnets": ["subnet-02b8cc3207f27f0c1", "subnet-03b878a0064ad3ae7", "subnet-08ff81cd86517585e"], "connectivity_info": [{"public_access": [{"type": "DISABLED"}]}], "ebs_volume_size": 110, "instance_type": "kafka.t3.small", "security_groups": ["sg-0ff5d25bab06b2667"], "storage_info": [{"ebs_storage_info": [{"provisioned_throughput": [], "volume_size": 110}]}]}], "client_authentication": [], "cluster_name": "msk-keeps", "configuration_info": [{"arn": "arn:aws:kafka:us-east-1:************:configuration/keeps-kafka-minimal/f56f1c96-dd25-49dc-996d-d18f83bfdae5-18", "revision": 4}], "current_version": "KQBWZHOQDJZ1C", "encryption_info": [{"encryption_at_rest_kms_key_arn": "arn:aws:kms:us-east-1:************:key/f46d3d08-186c-49fa-9fc8-50be51bad8ca", "encryption_in_transit": [{"client_broker": "PLAINTEXT", "in_cluster": true}]}], "enhanced_monitoring": "DEFAULT", "id": "arn:aws:kafka:us-east-1:************:cluster/msk-keeps/660fad9e-0867-4342-9210-b2f903b763c2-22", "kafka_version": "3.7.x", "logging_info": [{"broker_logs": [{"cloudwatch_logs": [], "firehose": [], "s3": [{"bucket": "msk-keeps-logs-404ec4ef787bb029", "enabled": true, "prefix": "logs/msk-"}]}]}], "number_of_broker_nodes": 3, "open_monitoring": [{"prometheus": [{"jmx_exporter": [{"enabled_in_broker": true}], "node_exporter": [{"enabled_in_broker": true}]}]}], "storage_mode": "LOCAL", "tags": {}, "tags_all": {}, "timeouts": null, "zookeeper_connect_string": "z-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181,z-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2181", "zookeeper_connect_string_tls": "z-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2182,z-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2182,z-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:2182"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo3MjAwMDAwMDAwMDAwLCJkZWxldGUiOjcyMDAwMDAwMDAwMDAsInVwZGF0ZSI6NzIwMDAwMDAwMDAwMH19", "dependencies": ["aws_s3_bucket.msk_bucket", "aws_security_group.msk_sg", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_id": "eipalloc-05b83679170a777c5", "association_id": "eipassoc-0b79a82dfadb18846", "connectivity_type": "public", "id": "nat-0059c413c49242a82", "network_interface_id": "eni-085a5e4a106ee91e5", "private_ip": "**************", "public_ip": "***********", "subnet_id": "subnet-04d84aa81e9ef855b", "tags": {"Name": "nat-gtw-eks"}, "tags_all": {"Name": "nat-gtw-eks"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_subnet.public-us-east-1a", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_network_acl", "name": "msk_nacl", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:network-acl/acl-0597bc34840b6f302", "egress": [{"action": "allow", "cidr_block": "***********/16", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "id": "acl-0597bc34840b6f302", "ingress": [{"action": "allow", "cidr_block": "***********/16", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "owner_id": "************", "subnet_ids": ["subnet-02b8cc3207f27f0c1", "subnet-03b878a0064ad3ae7", "subnet-08ff81cd86517585e"], "tags": {"Name": "msk-nacl"}, "tags_all": {"Name": "msk-nacl"}, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_network_acl_association", "name": "msk_nacl_assoc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "aclassoc-0907501fc7e9f9813", "network_acl_id": "acl-0597bc34840b6f302", "subnet_id": "subnet-02b8cc3207f27f0c1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 1, "schema_version": 0, "attributes": {"id": "aclassoc-06061e342ba838f84", "network_acl_id": "acl-0597bc34840b6f302", "subnet_id": "subnet-08ff81cd86517585e"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 2, "schema_version": 0, "attributes": {"id": "aclassoc-0547c24700bd0b882", "network_acl_id": "acl-0597bc34840b6f302", "subnet_id": "subnet-03b878a0064ad3ae7"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_subnet.msk_subnet", "aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}]}, {"mode": "managed", "type": "aws_network_acl_rule", "name": "egress_all_to_peered_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cidr_block": "***********/16", "egress": true, "from_port": 0, "icmp_code": null, "icmp_type": null, "id": "nacl-685912358", "ipv6_cidr_block": "", "network_acl_id": "acl-0597bc34840b6f302", "protocol": "-1", "rule_action": "allow", "rule_number": 100, "to_port": 0}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_vpc.main", "aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_network_acl_rule", "name": "ingress_all_from_peered_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cidr_block": "***********/16", "egress": false, "from_port": 0, "icmp_code": null, "icmp_type": null, "id": "nacl-**********", "ipv6_cidr_block": "", "network_acl_id": "acl-0597bc34840b6f302", "protocol": "-1", "rule_action": "allow", "rule_number": 100, "to_port": 0}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_network_acl.msk_nacl", "aws_vpc.main", "aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_route", "name": "msk_to_eks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "***********/16", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-0241de867aa60a4823901788224", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-0241de867aa60a482", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-msk"]}]}, {"mode": "managed", "type": "aws_route", "name": "public_to_msk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "***********/22", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-02e1975533ca1177d2602932349", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-02e1975533ca1177d", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-08c179a52947b249a", "id": "rtb-08c179a52947b249a", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0059c413c49242a82", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, {"carrier_gateway_id": "", "cidr_block": "***********/22", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, {"carrier_gateway_id": "", "cidr_block": "***********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-01425ae53b3021c72"}, {"carrier_gateway_id": "", "cidr_block": "**********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-0788322b771487858"}], "tags": {"Name": "private"}, "tags_all": {"Name": "private"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-02e1975533ca1177d", "id": "rtb-02e1975533ca1177d", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-00cfcaa80d9a569b9", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, {"carrier_gateway_id": "", "cidr_block": "***********/22", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-023ace3ce04b9b77d"}, {"carrier_gateway_id": "", "cidr_block": "***********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-01425ae53b3021c72"}, {"carrier_gateway_id": "", "cidr_block": "**********/16", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "instance_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": "pcx-0788322b771487858"}], "tags": {"Name": "public"}, "tags_all": {"Name": "public"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_internet_gateway.igw", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0706a5ec5f16c1083", "route_table_id": "rtb-08c179a52947b249a", "subnet_id": "subnet-07580e2fc7b205504", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_route_table.private", "aws_subnet.private-us-east-1a", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0a7cee6fefee522a0", "route_table_id": "rtb-08c179a52947b249a", "subnet_id": "subnet-08068e3da6b79ab27", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_route_table.private", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-058b0d404c49d5e69", "route_table_id": "rtb-02e1975533ca1177d", "subnet_id": "subnet-04d84aa81e9ef855b", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_subnet.public-us-east-1a", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-01eb4ccff17e4d864", "route_table_id": "rtb-02e1975533ca1177d", "subnet_id": "subnet-00684b0ce587ba6a1", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_subnet.public-us-east-1b", "aws_vpc.main", "aws_vpc.msk_vpc", "aws_vpc_peering_connection.eks-to-keeps", "aws_vpc_peering_connection.eks-to-msk", "aws_vpc_peering_connection.eks-to-rds"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "msk_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::msk-keeps-logs-404ec4ef787bb029", "bucket": "msk-keeps-logs-404ec4ef787bb029", "bucket_domain_name": "msk-keeps-logs-404ec4ef787bb029.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "msk-keeps-logs-404ec4ef787bb029.s3.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "70d9850736dda5b84e4f11ad8508126acf21eca55c3e1524ac19b24ef61a5256", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "msk-keeps-logs-404ec4ef787bb029", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Id\":\"AWSLogDeliveryWrite20150319\",\"Statement\":[{\"Action\":\"s3:PutObject\",\"Condition\":{\"ArnLike\":{\"aws:SourceArn\":\"arn:aws:logs:us-east-1:************:*\"},\"StringEquals\":{\"aws:SourceAccount\":\"************\",\"s3:x-amz-acl\":\"bucket-owner-full-control\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"delivery.logs.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::msk-keeps-logs-404ec4ef787bb029/logs/msk-/AWSLogs/************/*\",\"Sid\":\"AWSLogDeliveryWrite\"},{\"Action\":\"s3:GetBucketAcl\",\"Condition\":{\"ArnLike\":{\"aws:SourceArn\":\"arn:aws:logs:us-east-1:************:*\"},\"StringEquals\":{\"aws:SourceAccount\":\"************\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"delivery.logs.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::msk-keeps-logs-404ec4ef787bb029\",\"Sid\":\"AWSLogDeliveryAclCheck\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "msk_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0ff5d25bab06b2667", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0ff5d25bab06b2667", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "Allow Kafka traffic", "from_port": 9092, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 9092}], "name": "terraform-20240211222203966400000001", "name_prefix": "terraform-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Name": "msk-sg"}, "tags_all": {"Name": "msk-sg"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "msk_subnet", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-02b8cc3207f27f0c1", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-02b8cc3207f27f0c1", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "msk-subnet-0"}, "tags_all": {"Name": "msk-subnet-0"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-08ff81cd86517585e", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-08ff81cd86517585e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "msk-subnet-1"}, "tags_all": {"Name": "msk-subnet-1"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}, {"index_key": 2, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-03b878a0064ad3ae7", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1c", "availability_zone_id": "use1-az1", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-03b878a0064ad3ae7", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "msk-subnet-2"}, "tags_all": {"Name": "msk-subnet-2"}, "timeouts": null, "vpc_id": "vpc-065bdb039b06329cd"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.msk_vpc", "data.aws_availability_zones.msk_azs"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-07580e2fc7b205504", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "***********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-07580e2fc7b205504", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "private-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Name": "private-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-08068e3da6b79ab27", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-08068e3da6b79ab27", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "private-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Name": "private-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-us-east-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-04d84aa81e9ef855b", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-04d84aa81e9ef855b", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-us-east-1a", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-us-east-1b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-00684b0ce587ba6a1", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-00684b0ce587ba6a1", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-us-east-1b", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-us-east-1f", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-03e0588ec4f3ebf43", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1f", "availability_zone_id": "use1-az5", "cidr_block": "************/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-03e0588ec4f3ebf43", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-us-east-1f", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-us-east-1f", "kubernetes.io/cluster/keeps-eks-cluster": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-05621d5af0661cb6d", "assign_generated_ipv6_cidr_block": false, "cidr_block": "***********/16", "default_network_acl_id": "acl-02c1d5f706dd8a655", "default_route_table_id": "rtb-0afab0015fb750c43", "default_security_group_id": "sg-0c331b38c00c91912", "dhcp_options_id": "dopt-c57962a7", "enable_classiclink": false, "enable_classiclink_dns_support": false, "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-05621d5af0661cb6d", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0afab0015fb750c43", "owner_id": "************", "tags": {"Name": "main-eks-vpc"}, "tags_all": {"Name": "main-eks-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_vpc", "name": "msk_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-065bdb039b06329cd", "assign_generated_ipv6_cidr_block": false, "cidr_block": "***********/22", "default_network_acl_id": "acl-04d18bb91f1bd0072", "default_route_table_id": "rtb-0241de867aa60a482", "default_security_group_id": "sg-037c3f3bbeb12f38b", "dhcp_options_id": "dopt-c57962a7", "enable_classiclink": false, "enable_classiclink_dns_support": false, "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-065bdb039b06329cd", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0241de867aa60a482", "owner_id": "************", "tags": {"Name": "msk-vpc"}, "tags_all": {"Name": "msk-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_vpc_peering_connection", "name": "eks-to-keeps", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"accept_status": "active", "accepter": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "auto_accept": true, "id": "pcx-0788322b771487858", "peer_owner_id": "************", "peer_region": "us-east-1", "peer_vpc_id": "vpc-e7e87b82", "requester": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "tags": {"Name": "VPC Peering between EKS and Keeps"}, "tags_all": {"Name": "VPC Peering between EKS and Keeps"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMCwiZGVsZXRlIjo2MDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc_peering_connection", "name": "eks-to-msk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"accept_status": "active", "accepter": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "auto_accept": true, "id": "pcx-023ace3ce04b9b77d", "peer_owner_id": "************", "peer_region": "us-east-1", "peer_vpc_id": "vpc-065bdb039b06329cd", "requester": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "tags": {"Name": "VPC Peering between EKS and MSK"}, "tags_all": {"Name": "VPC Peering between EKS and MSK"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMCwiZGVsZXRlIjo2MDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main", "aws_vpc.msk_vpc"]}]}, {"mode": "managed", "type": "aws_vpc_peering_connection", "name": "eks-to-rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"accept_status": "active", "accepter": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "auto_accept": true, "id": "pcx-01425ae53b3021c72", "peer_owner_id": "************", "peer_region": "us-east-1", "peer_vpc_id": "vpc-068d31db956dfe39a", "requester": [{"allow_classic_link_to_remote_vpc": false, "allow_remote_vpc_dns_resolution": true, "allow_vpc_to_remote_classic_link": false}], "tags": {"Name": "VPC Peering between EKS and RDS"}, "tags_all": {"Name": "VPC Peering between EKS and RDS"}, "timeouts": null, "vpc_id": "vpc-05621d5af0661cb6d"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMCwiZGVsZXRlIjo2MDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "helm_release", "name": "metrics_server", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "metrics-server", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "metrics-server", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "0.7.2", "chart": "metrics-server", "first_deployed": **********, "last_deployed": **********, "name": "metrics-server", "namespace": "kube-system", "notes": "***********************************************************************\n* Metrics Server                                                      *\n***********************************************************************\n  Chart version: 3.12.2\n  App version:   0.7.2\n  Image tag:     registry.k8s.io/metrics-server/metrics-server:v0.7.2\n***********************************************************************\n", "revision": 1, "values": "{\"args\":[\"--kubelet-insecure-tls\",\"--kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname\"],\"resources\":{\"limits\":{\"cpu\":\"250m\",\"memory\":\"256Mi\"},\"requests\":{\"cpu\":\"100m\",\"memory\":\"128Mi\"}}}", "version": "3.12.2"}], "name": "metrics-server", "namespace": "kube-system", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": "https://kubernetes-sigs.github.io/metrics-server/", "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "upgrade_install": null, "values": ["args:\n  - --kubelet-insecure-tls\n  - --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname\nresources:\n  requests:\n    cpu: 100m\n    memory: 128Mi\n  limits:\n    cpu: 250m\n    memory: 256Mi\n"], "verify": false, "version": "3.12.2", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "helm_release", "name": "nginx-controller", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "https://github.com/kubernetes/ingress-nginx/releases/download/helm-chart-4.9.1/ingress-nginx-4.9.1.tgz", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "nginx-controller", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "1.9.6", "chart": "ingress-nginx", "first_deployed": **********, "last_deployed": **********, "name": "nginx-controller", "namespace": "default", "notes": "The ingress-nginx controller has been installed.\nIt may take a few minutes for the load balancer IP to be available.\nYou can watch the status by running 'kubectl get service --namespace default nginx-controller-ingress-nginx-controller --output wide --watch'\n\nAn example Ingress that makes use of the controller:\n  apiVersion: networking.k8s.io/v1\n  kind: Ingress\n  metadata:\n    name: example\n    namespace: foo\n  spec:\n    ingressClassName: nginx\n    rules:\n      - host: www.example.com\n        http:\n          paths:\n            - pathType: Prefix\n              backend:\n                service:\n                  name: exampleService\n                  port:\n                    number: 80\n              path: /\n    # This section is only required if TLS is to be enabled for the Ingress\n    tls:\n      - hosts:\n        - www.example.com\n        secretName: example-tls\n\nIf TLS is enabled for the Ingress, a Secret containing the certificate and key must also be provided:\n\n  apiVersion: v1\n  kind: Secret\n  metadata:\n    name: example-tls\n    namespace: foo\n  data:\n    tls.crt: <base64 encoded cert>\n    tls.key: <base64 encoded key>\n  type: kubernetes.io/tls\n", "revision": 1, "values": "{\"controller\":{\"autoscaling\":{\"enabled\":\"true\",\"maxReplicas\":\"5\",\"minReplicas\":\"3\"},\"service\":{\"annotations\":{\"service.beta.kubernetes.io/aws-load-balancer-backend-protocol\":\"http\",\"service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout\":\"60\",\"service.beta.kubernetes.io/aws-load-balancer-ssl-cert\":\"arn:aws:acm:us-east-1:************:certificate/f1bc42e4-8e77-4679-abda-626635d6017d\",\"service.beta.kubernetes.io/aws-load-balancer-ssl-ports\":\"https\",\"service.beta.kubernetes.io/aws-load-balancer-type\":\"nlb\"},\"targetPorts\":{\"http\":\"http\",\"https\":\"http\"}}}}", "version": "4.9.1"}], "name": "nginx-controller", "namespace": "default", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": null, "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [{"name": "controller.autoscaling.enabled", "type": "string", "value": "true"}, {"name": "controller.autoscaling.maxReplicas", "type": "string", "value": "5"}, {"name": "controller.autoscaling.minReplicas", "type": "string", "value": "3"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-backend-protocol", "type": "string", "value": "http"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-connection-idle-timeout", "type": "string", "value": "60"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-cert", "type": "string", "value": "arn:aws:acm:us-east-1:************:certificate/f1bc42e4-8e77-4679-abda-626635d6017d"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-ports", "type": "string", "value": "https"}, {"name": "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type", "type": "string", "value": "nlb"}, {"name": "controller.service.targetPorts.http", "type": "string", "value": "http"}, {"name": "controller.service.targetPorts.https", "type": "string", "value": "http"}], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "upgrade_install": null, "values": null, "verify": false, "version": "4.9.1", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "production", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "production", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "production", "resource_version": "1747548", "uid": "526c9d47-9c0c-4015-8c87-0f2ee162681c"}], "timeouts": null, "wait_for_default_service_account": null}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "security", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "security", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "security", "resource_version": "232067", "uid": "b87ed3fa-20fb-4e67-89e3-50009220bf44"}], "timeouts": null, "wait_for_default_service_account": null}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "stage", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "stage", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "stage", "resource_version": "8559", "uid": "63cdbb95-7314-4b54-b591-70bf88a7f747"}], "timeouts": null, "wait_for_default_service_account": null}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "local_file", "name": "kubeconfig", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "apiVersion: v1\nclusters:\n- cluster:\n    server: https://A06CAB855832EAF9D15509A904042B4E.gr7.us-east-1.eks.amazonaws.com\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1ERXhOVEl5TlRZek5Gb1hEVE16TURFeE1qSXlOVFl6TkZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBT01XCjJ2R2tkVndsR296d0FXcUMyc1I1Ri9tejhqS2NZOStrUmVtd1lsYzRPK0cvS3BCZ3ZwTFNtWXpUemJ6QzJJZ2gKbHhTUmhMdXZjdHRUdWhER25LQXJseFFjT2VROGxTaW5WekUySGc1RnBMRFFuY3RtNGxEdkYxUVcwY0lUak5HZgozZFVqZnRic2JmTEtIT3pSdGlWbWcrWlllbnFhWVdyWUhsaHRaSWxHa2FCb014dkRjbjdSOGxhY1ROR3NlUHRDCkxza2c0akhycTFpTVNvR3AxV3Vob3F3TVp0VnQ2Z0swTWpkMVRjSFVORmEwZXJLT3lRWjNWbXBidzhwT0pyNUwKaFhOSWlEYVNSc0tIUlJFV1hERHVoM09JYnMzNjQyMml0VS9HZVlKN3NXS0NvcEljMVBHRDlZd1RlbGRuTkEvTQpPNWZTSDVLZE1UWGNac1BoVFNjQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZDSEZLK0dsbFRVbE43WXRoay9WKzFBN3pVOThNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSFA2clFNbnlxTjNleXU1bmtLZApYRDZheThHRUxJNkRuOHVLc0h6Y3ByQjk3OWNnUnhuTW1lcllvVVc3RzMra3JTcXVSbHk5LzJUb2pFWjVNNklOClJFeS84dDYwTkF3aElESDlWTUJVKzV1R0tSMGw0VTBwTnZxTFRkYzlYTXdnKzN2VDNpVjMra3dJZ21nMnpKMDYKZ3JXS2NMQVE4Vkp1dENkbm9rQkFQTVdxazJyUXk2QWY0NkJNM2luV25NRHREakY1ZXZPTVV6RkhqUFgrUThxUgpFWmw4b25mdmZYZ1E3b2xVeG1FMjdGc0dOenF5dHlNOTJrazdmT2MyVXBCdzVmL3AwUG15VndGd2Q1WWQvRWpXCnJUejZPRlJqei85SVNDb3lxNkZjTG1iaC9vRmE4c2JXK0s2cUtoLzhQanVSY2NkYkdRcnEvVmRKMm5FWHdtL08KWFNFPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==\n  name: keeps-eks-cluster\ncontexts:\n- context:\n    cluster: keeps-eks-cluster\n    user: keeps-eks-cluster\n  name: keeps-eks-cluster\ncurrent-context: keeps-eks-cluster\nkind: Config\npreferences: {}\nusers:\n- name: keeps-eks-cluster\n  user:\n    exec:\n      apiVersion: client.authentication.k8s.io/v1beta1\n      command: aws-iam-authenticator\n      args:\n        - \"token\"\n        - \"-i\"\n        - \"keeps-eks-cluster\"\n        #- \"--role\"\n        #- \"arn:aws:iam::************:role/eks-cluster-keeps-eks-cluster\"\n\n", "content_base64": null, "content_base64sha256": "j/WkbFbjIWQyrlaO6DgNHfC3AM0NG0PB4r7nZADmLBw=", "content_base64sha512": "ilLpm0/NL/jW4QR8glbMhtaHy3OEERvp8smv1546tRZexmf4AOm7RCDRVs70+OjZzdNnw3nM0ORiTN4eovfYWA==", "content_md5": "5164e7208246154b1501e029b119de7e", "content_sha1": "e99ba6fe288dfb789de7590beabd0266ad9dfa8f", "content_sha256": "8ff5a46c56e3216432ae568ee8380d1df0b700cd0d1b43c1e2bee76400e62c1c", "content_sha512": "8a52e99b4fcd2ff8d6e1047c8256cc86d687cb7384111be9f2c9afd79e3ab5165ec667f800e9bb4420d156cef4f8e8d9cdd367c379ccd0e4624cde1ea2f7d858", "directory_permission": "0777", "file_permission": "0777", "filename": "kubeconfig", "id": "e99ba6fe288dfb789de7590beabd0266ad9dfa8f", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "sensitive_content"}]], "dependencies": ["aws_eks_cluster.cluster", "aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-us-east-1a", "aws_subnet.private-us-east-1b", "aws_subnet.public-us-east-1a", "aws_subnet.public-us-east-1b", "aws_vpc.main"]}]}, {"mode": "managed", "type": "random_id", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 0, "attributes": {"b64_std": "QE7E73h7sCk=", "b64_url": "QE7E73h7sCk", "byte_length": 8, "dec": "4633857599408418857", "hex": "404ec4ef787bb029", "id": "QE7E73h7sCk", "keepers": null, "prefix": null}, "sensitive_attributes": []}]}], "check_results": null}