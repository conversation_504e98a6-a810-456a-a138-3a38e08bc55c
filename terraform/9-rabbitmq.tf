
/* resource "helm_release" "rabbit" {
  name       = "rabbitmq"
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "rabbitmq"
  namespace  = kubernetes_namespace.stage.metadata[0].name

  depends_on = [
    aws_eks_node_group.private-nodes,
    kubernetes_namespace.stage
  ]
  set {
    name  = "auth.username"
    value = "user"
  }

  set {
    name  = "auth.password"
    value = "ptxom2d18ZLt"
  }

  set {
    name  = "persistence.size"
    value = "4Gi"
  }

  set {
    name  = "volumePermissions.enabled"
    value = "true"
  }
}
 */