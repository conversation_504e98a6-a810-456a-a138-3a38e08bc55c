# aws-iam-authenticator is required to install
# See: https://docs.aws.amazon.com/eks/latest/userguide/install-aws-iam-authenticator.html
# https://docs.aws.amazon.com/eks/latest/userguide/setting-up.html

locals {
  kubeconfig = <<KUBECONFIG
apiVersion: v1
clusters:
- cluster:
    server: ${aws_eks_cluster.cluster.endpoint}
    certificate-authority-data: ${aws_eks_cluster.cluster.certificate_authority[0].data}
  name: ${aws_eks_cluster.cluster.name}
contexts:
- context:
    cluster: ${aws_eks_cluster.cluster.name}
    user: ${aws_eks_cluster.cluster.name}
  name: ${aws_eks_cluster.cluster.name}
current-context: ${aws_eks_cluster.cluster.name}
kind: Config
preferences: {}
users:
- name: ${aws_eks_cluster.cluster.name}
  user:
    exec:
      apiVersion: client.authentication.k8s.io/v1beta1
      command: aws-iam-authenticator
      args:
        - "token"
        - "-i"
        - "keeps-eks-cluster"
        #- "--role"
        #- "arn:aws:iam::************:role/eks-cluster-keeps-eks-cluster"

  KUBECONFIG
}

resource "local_file" "kubeconfig" {
  filename = "kubeconfig"
  content = local.kubeconfig

  depends_on = [
    aws_eks_cluster.cluster
  ]
}

output "kubeconfig-message" {
   value = "Execute the following command 'cp kubeconfig ~/.kube/config' and run 'terraform apply' again"
   depends_on = [
    local_file.kubeconfig
  ]
}
