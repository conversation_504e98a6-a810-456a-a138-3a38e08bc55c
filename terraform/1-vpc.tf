resource "aws_vpc" "main" {
  cidr_block           = "***********/16"
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = {
    Name = "main-eks-vpc"
  }
}

resource "aws_vpc" "msk_vpc" {
  cidr_block = "***********/22"
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = {
    Name = "msk-vpc"
  }
}

 
resource "aws_vpc_peering_connection" "eks-to-rds" {
  depends_on = [
    aws_vpc.main
  ]
  auto_accept = true
  peer_vpc_id = "vpc-068d31db956dfe39a"
  vpc_id      = aws_vpc.main.id

  tags = {
    Name = "VPC Peering between EKS and RDS"
  }

  accepter {
    allow_remote_vpc_dns_resolution = true
  }

  requester {
    allow_remote_vpc_dns_resolution = true
  }
}

resource "aws_vpc_peering_connection" "eks-to-keeps" {
  depends_on = [
    aws_vpc.main
  ]
  auto_accept = true
  peer_vpc_id = "vpc-e7e87b82"
  vpc_id      = aws_vpc.main.id

  tags = {
    Name = "VPC Peering between EKS and Keeps"
  }

  accepter {
    allow_remote_vpc_dns_resolution = true
  }

  requester {
    allow_remote_vpc_dns_resolution = true
  }
}
 
 # VPC Peering Connection
resource "aws_vpc_peering_connection" "eks-to-msk" {
  depends_on = [
    aws_vpc.msk_vpc
  ]
  
  auto_accept = true
  peer_vpc_id = aws_vpc.msk_vpc.id
  vpc_id      = aws_vpc.main.id

  tags = {
    Name = "VPC Peering between EKS and MSK"
  }

  accepter {
    allow_remote_vpc_dns_resolution = true
  }

  requester {
    allow_remote_vpc_dns_resolution = true
  }
} 