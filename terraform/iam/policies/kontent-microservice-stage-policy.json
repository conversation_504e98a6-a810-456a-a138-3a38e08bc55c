{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["rekognition:DetectText", "rekognition:DetectLabels"], "Resource": "*"}, {"Effect": "Allow", "Action": ["comprehend:DetectSentiment", "comprehend:DetectEntities", "comprehend:DetectKeyPhrases", "comprehend:DetectSyntax", "comprehend:DetectDominantLanguage"], "Resource": "*"}, {"Effect": "Allow", "Action": ["transcribe:StartTranscriptionJob", "transcribe:GetTranscriptionJob", "transcribe:ListTranscriptionJobs"], "Resource": "*"}, {"Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject", "s3:ListBucket", "s3:DeleteObject"], "Resource": ["arn:aws:s3:::keeps.kontent.media.hml", "arn:aws:s3:::keeps.kontent.media.hml/*", "arn:aws:s3:::keeps.transcribe", "arn:aws:s3:::keeps.transcribe/*"]}, {"Effect": "Allow", "Action": ["mediaconvert:<PERSON><PERSON><PERSON><PERSON>", "mediaconvert:Get<PERSON>ob", "mediaconvert:ListJobs", "mediaconvert:DescribeEndpoints"], "Resource": "*"}, {"Effect": "Allow", "Action": "iam:PassRole", "Resource": "arn:aws:iam::503825601340:role/service-role/MediaConvert_Default_Role"}]}