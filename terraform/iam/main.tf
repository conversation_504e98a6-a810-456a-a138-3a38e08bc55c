resource "aws_iam_user" "rekognition_user" {
  name = var.user_name
}

resource "aws_iam_access_key" "rekognition_key" {
  user = aws_iam_user.rekognition_user.name
}

resource "aws_iam_policy" "rekognition_policy" {
  name   = "${var.user_name}-policy"
  policy = file("${path.module}/${var.policy_path}")
}

resource "aws_iam_user_policy_attachment" "rekognition_policy_attachment" {
  user       = aws_iam_user.rekognition_user.name
  policy_arn = aws_iam_policy.rekognition_policy.arn
}
