resource "aws_route_table" "private" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat.id
  }

  route {
    cidr_block     = "172.31.0.0/16"
    vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-keeps.id
  }

   route {
    cidr_block     = "163.166.0.0/16"
    vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-rds.id
  }

  route {
    cidr_block     = aws_vpc.msk_vpc.cidr_block
    vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-msk.id
  }
  
  tags = {
    Name = "private"
  }
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  route {
    cidr_block     = "172.31.0.0/16"
    vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-keeps.id
  }

   route {
    cidr_block     = "163.166.0.0/16"
    vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-rds.id
  }

  route {
    cidr_block     = aws_vpc.msk_vpc.cidr_block
    vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-msk.id
  }

  tags = {
    Name = "public"
  }
}

resource "aws_route_table_association" "private-us-east-1a" {
  subnet_id      = aws_subnet.private-us-east-1a.id
  route_table_id = aws_route_table.private.id
}

resource "aws_route_table_association" "private-us-east-1b" {
  subnet_id      = aws_subnet.private-us-east-1b.id
  route_table_id = aws_route_table.private.id
}

resource "aws_route_table_association" "public-us-east-1a" {
  subnet_id      = aws_subnet.public-us-east-1a.id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "public-us-east-1b" {
  subnet_id      = aws_subnet.public-us-east-1b.id
  route_table_id = aws_route_table.public.id
}



# Atualizar as tabelas de rota do VPC principal para permitir tráfego para o VPC do MSK
resource "aws_route" "public_to_msk" {
  route_table_id         = aws_route_table.public.id #aws_vpc.main.main_route_table_id # Substitua por sua tabela de rota específica se necessário
  destination_cidr_block = aws_vpc.msk_vpc.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-msk.id
}

/* resource "aws_route" "private_to_msk" {
  route_table_id         = aws_route_table.private.id #aws_vpc.main.main_route_table_id # Substitua por sua tabela de rota específica se necessário
  destination_cidr_block = aws_vpc.msk_vpc.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-msk.id
} */

# Atualizar as tabelas de rota do VPC do MSK para permitir tráfego de volta para o VPC principal
#resource "aws_route" "msk_to_main" {
#  count                  = length(aws_vpc.msk_vpc.route_table_ids)
#  route_table_id         = aws_vpc.msk_vpc.route_table_ids[count.index]
#  destination_cidr_block = aws_vpc.main.cidr_block
#  vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-msk.id
#}
resource "aws_route" "msk_to_eks" {
  route_table_id         = aws_vpc.msk_vpc.main_route_table_id #aws_vpc.main.main_route_table_id # Substitua por sua tabela de rota específica se necessário
  destination_cidr_block = aws_vpc.main.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.eks-to-msk.id
}

