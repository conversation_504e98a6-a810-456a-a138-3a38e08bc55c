# Run this first helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
# Versoes disponiveis: helm search repo prometheus-community/kube-prometheus-stack --versions
# helm repo update
resource "helm_release" "prometheus" {
  name             = "prometheus"
  chart            = "prometheus-community/kube-prometheus-stack"
  namespace        = "monitoring"
  create_namespace = true
  version          = "68.3.0"
  values = [
    <<EOF
grafana:
  persistence:
    enabled: true
    accessModes:
      - ReadWriteOnce
    size: 10Gi
    storageClassName: "gp3"
    annotations: {}
    finalizers:
      - kubernetes.io/pvc-protection

prometheus:
  prometheusSpec:
    retention: "15d"
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: "gp3"
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 20Gi
EOF
  ]

  depends_on = [
    aws_eks_node_group.m6i-nodes,
  ]
}
 