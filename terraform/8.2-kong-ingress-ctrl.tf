 #   resource "kubernetes_namespace" "kong" {
#      metadata {
#        name = "kong"
#      }
#    }
#    
#    resource "helm_release" "kong" {
#      name       = "kong"
#      repository = "https://charts.konghq.com"
#      chart      = "kong"
#      namespace  = kubernetes_namespace.kong.metadata[0].name
#    
#      # Habilita o controlador de ingress
#      set {
#        name  = "ingressController.enabled"
#        value = "true"
#      }
#    
#      # Define o tipo de serviço como LoadBalancer (NLB)
#      set {
#        name  = "proxy.type"
#        value = "LoadBalancer"
#      }
#    
#      # Configura as portas HTTP e HTTPS
#      set {
#        name  = "proxy.http.enabled"
#        value = "true"
#      }
#    
#      set {
#        name  = "proxy.tls.enabled"
#        value = "true"
#      }
#    
#      # Configurações do Load Balancer (NLB) e SSL
#      set {
#        name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type"
#        value = "nlb"
#      }
#    
#      set {
#        name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-backend-protocol"
#        value = "http"
#      }
#    
#      set {
#        name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-ports"
#        value = "https"
#      }
#    
#      set {
#        name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-ssl-cert"
#        value = "arn:aws:acm:us-east-1:503825601340:certificate/f1bc42e4-8e77-4679-abda-626635d6017d"
#      }
#    
#      set {
#        name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-connection-idle-timeout"
#        value = "60"
#      }
#    
#      # Configuração de autoscaling
#      set {
#        name  = "proxy.replicaCount"
#        value = "3"
#      }
#    
#      set {
#        name  = "autoscaling.enabled"
#        value = "true"
#      }
#    
#      set {
#        name  = "autoscaling.minReplicas"
#        value = "3"
#      }
#    
#      set {
#        name  = "autoscaling.maxReplicas"
#        value = "5"
#      }
#    
#      # Desabilita a administração do Kong
#      set {
#        name  = "admin.enabled"
#        value = "false"
#      }
#    
#      # Modo sem banco de dados
#      set {
#        name  = "env.database"
#        value = "off"
#      }
#    
#      set {
#        name  = "service.externalTrafficPolicy"
#        value = "Local"
#      }
#    
#      depends_on = [kubernetes_namespace.kong]
#    }
#    