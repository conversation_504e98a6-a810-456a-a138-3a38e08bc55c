# Run this first: helm repo add sonarqube https://SonarSource.github.io/helm-chart-sonarqube
resource "helm_release" "sonarqube" {
  name      = "sonarqube"
  chart     = "sonarqube/sonarqube"
  namespace = "qa"
  create_namespace = true
  version = "2025.3.0"

  depends_on = [
    aws_eks_node_group.m6i-nodes
  ]

  values = [
    <<EOF
sonarProperties:
  sonar.jdbc.maxActive: 5   # Definir o número máximo de conexões ativas
  sonar.jdbc.maxIdle: 2     # Definir o número máximo de conexões inativas
  sonar.jdbc.minIdle: 1     # Definir o número mínimo de conexões inativas
EOF
  ]

  set {
    name  = "postgresql.enabled"
    value = "false"
  }
  set {
    name  = "jdbcOverwrite.enabled"
    value = "true"
  }
  set {
    name  = "jdbcOverwrite.jdbcUrl"
    value = "***************************************************************************************************************"
  }
  set {
    name  = "jdbcOverwrite.jdbcUsername"
    value = "postgres"
  }
  set {
    name  = "jdbcOverwrite.jdbcPassword"
    value = "1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg"
  }
  set {
    name  = "resources.requests.memory"
    value = "1000M"
  }
  set {
    name  = "resources.limits.memory"
    value = "3000M"
  }

  set {
    name  = "resources.limits.cpu"
    value = "1500m"
  }
  
  set {
    name  = "monitoringPasscode"
    value = "3y12qzIoSdIw"
  }

  set {
    name  = "monitoringPasscodeSecretName"
    value = "sonar-passcode-name"
  }

  set {
    name  = "monitoringPasscodeSecretKey"
    value = "sonar-passcode-key"
  }

  set {
    name  = "community.enabled"
    value = "true"
  }

}