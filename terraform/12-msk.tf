// https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/msk_cluster.html#configuration_info

data "aws_availability_zones" "msk_azs" {
  state = "available"
}

# Subnets para MSK
resource "aws_subnet" "msk_subnet" {
  count = 3

  availability_zone = data.aws_availability_zones.msk_azs.names[count.index]
  cidr_block        = "157.128.${count.index}.0/24"
  vpc_id            = aws_vpc.msk_vpc.id

  tags = {
    Name = "msk-subnet-${count.index}"
  }
}

# Security Group para MSK com regras básicas de entrada/saída
resource "aws_security_group" "msk_sg" {
  vpc_id = aws_vpc.msk_vpc.id

  # Permitindo tráfego de entrada Kafka
  ingress {
    from_port   = 9092
    to_port     = 9092
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] 
    #cidr_blocks = [aws_vpc.main.cidr_block]
    description = "Allow Kafka traffic"
  }

  # Permitindo todo o tráfego de saída
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "msk-sg"
  }
}

# NACL para subnets do MSK
resource "aws_network_acl" "msk_nacl" {
  vpc_id = aws_vpc.msk_vpc.id

  tags = {
    Name = "msk-nacl"
  }
}
resource "aws_network_acl_association" "msk_nacl_assoc" {
  count = length(aws_subnet.msk_subnet.*.id)

  network_acl_id = aws_network_acl.msk_nacl.id
  subnet_id      = aws_subnet.msk_subnet[count.index].id
}

# Permitir todo o tráfego de entrada do VPC Peering
resource "aws_network_acl_rule" "ingress_all_from_peered_vpc" {
  network_acl_id = aws_network_acl.msk_nacl.id
  rule_number    = 100
  rule_action    = "allow"
  egress         = false
  protocol       = "-1" # Todos os protocolos
  cidr_block     = aws_vpc.main.cidr_block # Substitua pelo CIDR do VPC peered
  from_port      = 0
  to_port        = 0
}

# Permitir todo o tráfego de saída para o VPC Peering
resource "aws_network_acl_rule" "egress_all_to_peered_vpc" {
  network_acl_id = aws_network_acl.msk_nacl.id
  rule_number    = 100
  rule_action    = "allow"
  egress         = true
  protocol       = "-1" # Todos os protocolos
  cidr_block     = aws_vpc.main.cidr_block # Substitua pelo CIDR do VPC peered
  from_port      = 0
  to_port        = 0
}

resource "aws_msk_cluster" "msk_cluster" {
  cluster_name           = "msk-keeps"
  kafka_version          = "3.7.x"
  number_of_broker_nodes = 3

  broker_node_group_info {
    instance_type  = "kafka.t3.small"
    client_subnets = [aws_subnet.msk_subnet[0].id, aws_subnet.msk_subnet[1].id, aws_subnet.msk_subnet[2].id]
   storage_info {
      ebs_storage_info {
        volume_size = 110
      }
    }
    security_groups = [aws_security_group.msk_sg.id]
  }
  encryption_info {
    encryption_in_transit {
      client_broker = "PLAINTEXT"
    }
  }

  configuration_info {
    arn = "arn:aws:kafka:us-east-1:503825601340:configuration/keeps-kafka-minimal/f56f1c96-dd25-49dc-996d-d18f83bfdae5-18"
    revision = 5
  }

  open_monitoring {
    prometheus {
      jmx_exporter {
        enabled_in_broker = true
      }
      node_exporter {
        enabled_in_broker = true
      }
    }
  }

  logging_info {
    broker_logs {
      s3 {
        enabled = true
        bucket  = aws_s3_bucket.msk_bucket.id
        prefix  = "logs/msk-"
      }
    }
  }
}

# S3 Bucket para Logs do MSK
resource "aws_s3_bucket" "msk_bucket" {
  # provider = aws.west // https://saturncloud.io/blog/terraform-aws-s3-bucket-overcoming-region-discrepancies/
  bucket = "msk-keeps-logs-${random_id.bucket_suffix.hex}"
}

resource "random_id" "bucket_suffix" {
  byte_length = 8
}

output "zookeeper_connect_string" {
  value = aws_msk_cluster.msk_cluster.zookeeper_connect_string
}

output "bootstrap_brokers_tls" {
  description = "TLS connection host:port pairs"
  value       = aws_msk_cluster.msk_cluster.bootstrap_brokers_tls
}
