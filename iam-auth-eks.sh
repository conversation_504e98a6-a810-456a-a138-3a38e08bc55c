#!/bin/bash
export cluster_endpoint=https://284E2743C7F1E657BD52B09018822CC9.gr7.us-east-1.eks.amazonaws.com
export certificate_data=LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJeU1UQXlNREUzTkRReU0xb1hEVE15TVRBeE56RTNORFF5TTFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTGJyCmpnWEQyUzZGK3ZGR0ZuNjdOSnoxQTk4RVBqTXFEVDFoRE9QaGdoelVsZHRacGpnTlo1bW8zTVdodStUSGFiWTQKck90clg0YzNqRUdQSjZMUi9rWk15eE9jZ2RaMUorMHdQbGtBcWwrUEN1QmI0NGtIS2V3WVpkS25NNzluaUZLaAp3TWtoL1NyR3lscXJmL3FsckdoMEdUdFI4SThmNG1yTFpGUm12Y0JUY1llSWdITzk5U09nL3pHYWxyZmVJeFJpCnBRQ3A4YlhsTXVZME9SY0VLVUhwTWNiZzhSNkgwWDRlbWMxV3lEbEVtTmZCZjJHdGRkTUdHZUFQZmhnQ1gxam0KSmUxNG9XalI4eDFwbHZPWS9ESXdVREVVcFlmNndsOFFRZEs1K3Y2enRLZHJhZndSWmtCWFN5V3FsVXcvSldtVwp1Vlo3azV0VG1JUm41Zi9lQXFjQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZIZTJIWHJaL3BzZ1RaT3IwdTIxbmZzb2QvWk1NQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRW0rb1ppdmxmOTVJR1ZUL0lrQQprTndxVTBOMEhEcmlkV0RsRXpncmZHcStqUnpzdWF6ZkZsaW9xTkQvSXNlRGg1QisyZVFwdlU1dlM1RzdIUURtCkNLdjZrbkRXdktYTTlidUxzR09TYTl5OGRXc293NVpwcG5Uam56SlBEK0xzb2VPVmZPZ1hxM2o1UDdkcjVpRjQKZG1PV3RkSkFKU2xGazFoUzhTRHZEdTVuTHBjVUszT2M5YVJnTWhWNTYxTTZXb0xBSkQ2bnhrRjVuVno3ZXU0eQp6U1A2SlU3djUxaVg0V2toemFpcEwvZWRKS1RHeDBnbENJOHVjcFRvWkZsNTR0TnJNOTdQVC9ROEpuanNqOWVCCjhsa3lGVUhhMUVKclh1TDF5ZjRtRURUTTAxUmJDWHVhYjc2YjcwdzkwR2dkeVNuWjBWdDNDSVVEUXpxcGpWcVcKT3FvPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
export account_id=************
export region_code=us-east-1
export cluster_name=keeps-eks-cluster

read -r -d '' KUBECONFIG <<EOF
apiVersion: v1
clusters:
- cluster:
    server: $cluster_endpoint
    certificate-authority-data: $certificate_data
  name: arn:aws:eks:$region_code:$account_id:cluster/$cluster_name
contexts:
- context:
    cluster: arn:aws:eks:$region_code:$account_id:cluster/$cluster_name
    user: arn:aws:eks:$region_code:$account_id:cluster/$cluster_name
  name: arn:aws:eks:$region_code:$account_id:cluster/$cluster_name
current-context: arn:aws:eks:$region_code:$account_id:cluster/$cluster_name
kind: Config
preferences: {}
users:
- name: arn:aws:eks:$region_code:$account_id:cluster/$cluster_name
  user:
    exec:
      apiVersion: client.authentication.k8s.io/v1beta1
      command: aws-iam-authenticator
      args:
        - "token"
        - "-i"
        - "$cluster_name"
        - "--role"
        - "arn:aws:iam::$account_id:role/eks-cluster-keeps-eks-cluster"
EOF
echo "${KUBECONFIG}" > ~/.kube/config
