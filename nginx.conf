server {
    listen 80;
    server_name localhost;
    client_max_body_size 600M;

    location / {
        proxy_pass http://keycloak:8080/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;

        #proxy_read_timeout 90;

        # proxy header
        #proxy_set_header X-Real-IP $remote_addr;
        #proxy_set_header X-Forwarded-For $proxy_protocol_addr;
        #proxy_set_header X-Scheme $scheme;
        #proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /app/myaccount/staticfiles/;
    }
}