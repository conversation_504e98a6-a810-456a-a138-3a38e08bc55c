apiVersion: apps/v1
kind: Deployment
metadata:
  name: konquest-worker
  namespace: stage
  labels:
    app: konquest-worker
spec:
  selector:
    matchLabels:
      app: konquest-worker
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: konquest-worker
    spec:
      volumes:
        - name: konquest-secret-vol
          secret:
            secretName: konquest-secret
      containers:
        - name: konquest-worker
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/konquest/konquest-server:stage"
          imagePullPolicy: Always

          envFrom:
            - secretRef:
                name: konquest-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m
          command:
            [
              "/usr/bin/supervisord",
              "-c",
              "/etc/supervisord.conf"
            ]