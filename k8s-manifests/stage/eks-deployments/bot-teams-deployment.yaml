apiVersion: v1
kind: Service
metadata:
  name: bot-teams-svc
  namespace: stage
  labels:
    app: bot-teams
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3978
      targetPort: 3978
  selector:
    app: bot-teams
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: bot-teams-secret
  namespace: stage
type: Opaque
stringData:
  TEAMS_APP_ID: 84b03e81-0d92-4221-bd6d-0c3c59b6fe8e
  TEAMS_APP_TENANT_ID: 8bb6599d-07f1-4a42-a19c-a66727015387
  BOT_ID: 3dc00da9-a096-4ce7-9441-bd7bc0ed6517
  BOT_PASSWORD: ****************************************
  CONNECTION_STRING: **********************************************************************/bot_teams_dev_db

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bot-teams
  namespace: stage
  labels:
    app: bot-teams
spec:
  selector:
    matchLabels:
      app: bot-teams
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: bot-teams
    spec:
      volumes:
        - name: bot-teams-secret-vol
          secret:
            secretName: bot-teams-secret
      containers:
        - name: bot-teams
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps-bot-teams:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3978
          envFrom:
            - secretRef:
                name: bot-teams-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 100m