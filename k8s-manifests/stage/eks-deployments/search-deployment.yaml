apiVersion: v1
kind: Service
metadata:
  name: search-svc
  namespace: stage
  labels:
    app: search
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: search
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: search-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: development
  ENV_SUFFIX: stage

  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_CLIENT_ID: keeps-search-api-stage
  AUTH_CLIENT_SECRET: oGaj16qNvYZ9wHMv5gErxkGKuo1gf3mF
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB

  ELASTICSEARCH_SERVER: https://keeps.es.us-east-1.aws.found.io
  ELASTICSEARCH_USER: elastic
  ELASTICSEARCH_PASSWORD: Ztx2l2zLkf2DkmQYuAYRR0Ty

  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  REDIS_TTL_MS: "10000"

  KONQUEST_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  ANALYTICS_ID: c2928f23-a5a6-4f59-94a7-7e409cf1d4f4
  MYACCOUNT_SUPER_TOKEN: 637a2f9e72daba2ebb03a699c7a4c08d

  #OLD_MYACCOUNT_API_URL: http://myaccount-svc:8000
  #MYACCOUNT_URL: http://myaccount-svc:8000
  #MYACCOUNT_API_URL: http://myaccount-v2-svc:3000

  OLD_MYACCOUNT_API_URL: https://learning-platform-api-stage.keepsdev.com/myaccount
  MYACCOUNT_URL: https://learning-platform-api-stage.keepsdev.com/myaccount
  MYACCOUNT_API_URL: https://learning-platform-api-stage.keepsdev.com/myaccount-v2
  CUSTOM_SECTIONS_GRPC_SERVER_URL: custom-sections-svc:50051


---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: search
  namespace: stage
  labels:
    app: search
spec:
  selector:
    matchLabels:
      app: search
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: search
    spec:
      volumes:
        - name: search-secret-vol
          secret:
            secretName: search-secret
      containers:
        - name: search
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-search:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: search-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

