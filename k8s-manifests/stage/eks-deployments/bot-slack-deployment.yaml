apiVersion: v1
kind: Service
metadata:
  name: bot-slack-svc
  namespace: stage
  labels:
    app: bot-slack
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3978
      targetPort: 3978
  selector:
    app: bot-slack
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: bot-slack-secret
  namespace: stage
type: Opaque
stringData:
  SLACK_CLIENT_ID: "489706589072.7171111199475"
  SLACK_CLIENT_SECRET: e6c561828377656b44ed04f71e60ef5e
  SLACK_SIGNING_SECRET: d7645663b7aeb5a838786e0ab518fe44
  SLACK_OAUTH_REDIRECT_URL: https://learning-platform-api-stage.keepsdev.com/slack/slack/oauth_redirect
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_NAME: bot_slack_dev_db
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  


---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bot-slack
  namespace: stage
  labels:
    app: bot-slack
spec:
  selector:
    matchLabels:
      app: bot-slack
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: bot-slack
    spec:
      volumes:
        - name: bot-slack-secret-vol
          secret:
            secretName: bot-slack-secret
      containers:
        - name: bot-slack
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps-bot-slack:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3978
          envFrom:
            - secretRef:
                name: bot-slack-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 128Mi
              cpu: 100m