apiVersion: apps/v1
kind: Deployment
metadata:
  name: smartzap-data-sync
  namespace: stage
  labels:
    app: smartzap-data-sync
spec:
  selector:
    matchLabels:
      app: smartzap-data-sync
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: smartzap-data-sync
    spec:
      volumes:
        - name: keeps-kafka-connector-secret-vol
          secret:
            secretName: keeps-kafka-connector-secret
      containers:
        - name: smartzap-data-sync
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/data-indexer:stage"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: keeps-kafka-connector-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 50m
            limits:
              memory: 256Mi
              cpu: 200m
          command: ["python", "-u", "./app-smartzap-data-sync.py"]
          