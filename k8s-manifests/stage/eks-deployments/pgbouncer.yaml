apiVersion: v1
kind: Service
metadata:
  name: pgbouncer-svc
  namespace: stage
  labels:
    app: pgbouncer
spec:
  ports:
    - port: 5432
      targetPort: 5432
  selector:
    app: pgbouncer
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pgbouncer-config
  namespace: stage
data:
  pgbouncer.ini: |
    [databases]
    konquest_db = host=postgresrdsdev port=5432 dbname=konquest_dev_db user=postgres password=1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg

    [pgbouncer]
    listen_addr = 0.0.0.0
    listen_port = 5432
    auth_type = md5
    auth_file = /etc/pgbouncer/userlist.txt

    # Usa pooling no nível de transação
    pool_mode = transaction

    # Limite total de conexões aceitas pelo PgBouncer (max_db_connections * 3)
    max_client_conn = 120  

    # Distribuição das conexões reais entre os bancos
    default_pool_size = 40
    reserve_pool_size = 5
    reserve_pool_timeout = 5

    # Limite total de conexões reais com o banco (Alinhado com o limite do RDS)
    max_db_connections = 45

    # Fe<PERSON>r conex<PERSON>es inativas após 60 segundos
    server_idle_timeout = 60 
    
    log_connections = 1
    log_disconnections = 1
  userlist.txt: |
    "postgres" "1cc4b910cfc1bd45cd3f9628992d447e"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer
  namespace: stage
  labels:
    app: pgbouncer
spec:
  selector:
    matchLabels:
      app: pgbouncer
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: pgbouncer
    spec:
      volumes:
        - name: pgbouncer-config-vol
          configMap:
            name: pgbouncer-config
      containers:
        - name: pgbouncer
          image: edoburu/pgbouncer:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 5432
          volumeMounts:
            - name: pgbouncer-config-vol
              mountPath: /etc/pgbouncer
          
