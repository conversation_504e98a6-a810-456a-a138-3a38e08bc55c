apiVersion: v1
kind: Service
metadata:
  name: integration-gateway-alura-svc
  namespace: stage
  labels:
    app: integration-gateway-alura
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000
  selector:
    app: integration-gateway-alura
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: integration-gateway-alura-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: stage
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.cloud.es.io
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_ENVIRONMENT: stage
  
  # DB
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_NAME: integration_gateway_alura_dev_db
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_DIALECT: postgres
  DB_DEBUG: "false"
  MIGRATIONS_RUN: "true"

  #FOR CHECK SIGNATURES
  TOKEN_ALURA_GET_ALL_COURSES: c8996aa897594fae93f0998d3154ca66
  TOKEN_ALURA_GET_FINISHED_ENROLLMENTS: 16a575253691414d8b11549b21c2d548
  TOKEN_ALURA_GET_PROGRESS_ENROLLMENTS: f48ac309d3114a96b18865923fa2457c
  TOKEN_ALURA_SSO: 4adfa8f52b634156a94d143bee5130fa
  TOKEN_ALURA_GET_ENROLLMENTS_COURSES: 5a5c234ef5734e18bfc5e3cf9edcfa2a
  TOKEN_ALURA_GET_ENROLLMENTS_ENGAGEMENT: 28f49c923329495b84e1ad2d3e48e38d

  # AUTHENTICATION
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  AUTH_CLIENT_ID: konquest
  AUTH_CLIENT_SECRET: 6128ac38-3fcf-4712-81e9-8e307d9bd849
  AUTH_DEBUG: "true"

  # USER AUTHORIZATION
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  OLD_MYACCOUNT_API_URL: http://myaccount-svc:8000
  KONQUEST_APPLICATION_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  MYACCOUNT_SUPER_TOKEN: ********************************

  # RABBIT
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  RABBITMQ_BELL_NOTIFICATIONS_QUEUE: bell
  BELL_NOTIFICATIONS_QUEUE_IS_DURABLE: "true"

  # KONQUEST INTEGRATION
  KEEPS_SECRET_TOKEN_INTEGRATION: ********************************
  KONQUEST_URL: http://konquest-svc:8000

  # OTHERS
  ALURA_API_URL: https://cursos.alura.com.br/corp/api/v1
  ALURA_SSO_URL: https://cursos.alura.com.br/
  GROUP_NAME_ALURA: Alura Integration
  USER_OWNER_ALURA_INTEGRATION_ID: 1b26d935-f87c-4e2a-94e9-e1d377cc5e16
  ALURA_PROVIDER_ID: b7927b8c-20dd-431a-a006-1bebab0c7d64

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: integration-gateway-alura
  namespace: stage
  labels:
    app: integration-gateway-alura
spec:
  selector:
    matchLabels:
      app: integration-gateway-alura
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: integration-gateway-alura
    spec:
      volumes:
        - name: integration-gateway-alura-secret-vol
          secret:
            secretName: integration-gateway-alura-secret
      containers:
        - name: integration-gateway-alura
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps-integration-gateway-alura:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: integration-gateway-alura-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m