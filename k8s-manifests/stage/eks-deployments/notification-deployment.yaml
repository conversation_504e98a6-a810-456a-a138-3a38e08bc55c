apiVersion: v1
kind: Service
metadata:
  name: notification-svc
  namespace: stage
  labels:
    app: notification
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: notification
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: notification-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: stage
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_CLIENT_ID: keeps-notification-api-stage
  AUTH_CLIENT_SECRET: BQFNH2MvTNLiBd7narlwEW24ttszaL6p
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_NAME: notification_dev_db
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_DIALECT: postgres
  AWS_SNS_TOPIC_ARN: arn:aws:sns:us-east-1:************:EmailMessageEvents
  APP_URL: https://learning-platform-api-stage.keepsdev.com/notification
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.cloud.es.io
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_ENVIRONMENT: stage
  TEMPLATES_PATH: "assets/email-templates"
  EMAIL_TEMPLATES_PATH: "assets/email-templates" #TODO: remove me
  EMAIL_TEMPLATES_PARTIALS_PATH: "assets/email-templates/partials"
  LOCALE_PATH: "assets/i18n"
  AWS_SES_SENDER: "Plataforma Aprendizagem <<EMAIL>>"
  AWS_SES_REGION: "us-east-1"
  AWS_SECRET_ACCESS_KEY: "kSiw9hpPegtd9D2EqdqVpGgNNn7ObqQF2haNBfaf"
  AWS_ACCESS_KEY_ID: "********************"
  I18N_PATH: "assets/i18n"
  MESSAGE_SENT_TRACK_TIMEOUT: "500"
  CRYPTO_SECRET_KEY: TxiGM7HNogTHnJsn
  MIGRATIONS_RUN: "true"
  AUTH_APPLICATION_ID: "ad7e5ad2-1552-43ab-a471-710954f0e66a"
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  RABBITMQ_BELL_NOTIFICATIONS_QUEUE: bell
  BELL_NOTIFICATIONS_QUEUE_IS_DURABLE: "true"
  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "637a2f9e72daba2ebb03a699c7a4c08d"
  BOT_SLACK: http://bot-slack-svc:3978/api/notification
  BOT_TEAMS: http://bot-teams-svc:3978/api/notification
  SERVICE_TWILIO_SENDER_PHONE: "***********"
  SERVICE_TWILIO_ACCOUNT_SID: "**********************************"
  SERVICE_TWILIO_TEMPLATES_PATH: assets/twilio-templates.json
  SERVICE_TWILIO_KEY_SID: "**********************************"
  SERVICE_TWILIO_KEY_SECRET: "********************************"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification
  namespace: stage
  labels:
    app: notification
spec:
  selector:
    matchLabels:
      app: notification
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: notification
    spec:
      volumes:
        - name: notification-secret-vol
          secret:
            secretName: notification-secret
      containers:
        - name: notification
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-notification:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: notification-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

