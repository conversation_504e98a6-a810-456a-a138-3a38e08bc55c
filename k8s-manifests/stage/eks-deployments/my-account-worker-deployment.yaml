apiVersion: apps/v1
kind: Deployment
metadata:
  name: myaccount-worker
  namespace: stage
  labels:
    app: myaccount-worker
spec:
  selector:
    matchLabels:
      app: myaccount-worker
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: myaccount-worker
    spec:
      volumes:
        - name: myaccount-secret-vol
          secret:
            secretName: myaccount-secret
      containers:
        - name: myaccount-worker
          image: "************.dkr.ecr.us-east-1.amazonaws.com/account/my-account-server:stage"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: myaccount-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: "500m"
          command:
            [
              "/usr/bin/supervisord",
              "-c",
              "/etc/supervisord.conf"
            ]