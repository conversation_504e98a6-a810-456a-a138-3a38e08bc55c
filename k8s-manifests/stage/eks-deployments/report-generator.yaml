apiVersion: v1
kind: Service
metadata:
  name: report-generator-svc
  namespace: stage
  labels:
    app: report-generator
spec:
  ports:
  - name: http
    protocol: TCP
    port: 8080
    targetPort: 8080
  selector:
    app: report-generator
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: report-generator-secret
  namespace: stage
type: Opaque
stringData:
  ENV_SUFFIX: stage

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: report-generator
  namespace: stage
  labels:
    app: report-generator
spec:
  selector:
    matchLabels:
      app: report-generator
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: report-generator
    spec:
      volumes:
        - name: report-generator-secret-vol
          secret:
            secretName: report-generator-secret
      containers:
        - name: report-generator
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/report-generator:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - secretRef:
                name: report-generator-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 512Mi
              cpu: 500m

