apiVersion: v1
kind: Service
metadata:
  name: analytics-ai-svc
  namespace: stage
  labels:
    app: analytics-ai
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: analytics-ai
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: analytics-ai-secret
  namespace: stage
type: Opaque
stringData:
  ENVIRONMENT: staging
  OPENAI_API_KEY: "************************************************************************************************************************************"
  DB_USER: postgres
  DB_PASSWORD: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_NAME: analytics_ai_dev_db
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  KEYCLOAK_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  KEYCLOAK_ISS: https://iam.keepsdev.com/auth/realms/keeps-dev
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-ai
  namespace: stage
  labels:
    app: analytics-ai
spec:
  selector:
    matchLabels:
      app: analytics-ai
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: analytics-ai
    spec:
      volumes:
        - name: analytics-ai-secret-vol
          secret:
            secretName: analytics-secret
      containers:
        - name: analytics-ai
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learn-analytics-ai:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: analytics-ai-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1
