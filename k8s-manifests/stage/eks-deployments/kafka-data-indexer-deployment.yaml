apiVersion: apps/v1
kind: Deployment
metadata:
  name: keeps-kafka-data-indexer
  namespace: stage
  labels:
    app: keeps-kafka-data-indexer
spec:
  selector:
    matchLabels:
      app: keeps-kafka-data-indexer
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: keeps-kafka-data-indexer
    spec:
      volumes:
        - name: keeps-kafka-connector-secret-vol
          secret:
            secretName: keeps-kafka-connector-secret
      containers:
        - name: keeps-kafka-data-indexer
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/data-indexer:stage"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: keeps-kafka-connector-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m
