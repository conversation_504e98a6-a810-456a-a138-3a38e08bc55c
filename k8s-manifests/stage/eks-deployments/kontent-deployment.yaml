apiVersion: v1
kind: Service
metadata:
  name: kontent-svc
  namespace: stage
  labels:
    app: kontent
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: kontent
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: kontent-secret
  namespace: stage
type: Opaque
stringData:
  DEBUG: "1"
  LOG_LEVEL: error

  AWS_BUCKET_NAME: keeps.kontent.media.hml
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: OnPbj1rqGm9dXLoV5dhM3Irizs6Jo3PvdP84s+hD
  AWS_REGION_NAME: us-east-1
  AWS_TRANSCRIBE_BUCKET: keeps.transcribe
  AWS_TRANSCRIBE_THRESHOLD: "0.5"
  AWS_TRANSCRIBE_VOCABULARY: keeps_pt_br
  CELERY_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_NAMESPACE: CELERY
  CELERY_QUEUE: kontent-stage
  CELERY_RESULT_SERIALIZER: json
  CELERY_TASK_SERIALIZER: json
  CELERY_TIMEZONE: UTC
  CLOUD_CONVERT_KEY: FQ3vCTzlVViKxVlWvLh9S54ySoxFMIydJqNwy2aKZrg8ln7e2kZXY08xi5XAUWRs
  DATABASE_HOST: postgresrdsdev
  DATABASE_NAME: kontent_dev_db
  DATABASE_PASSWORD: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DATABASE_PORT: "5432"
  DATABASE_USER: postgres
  ELASTICSEARCH_HOST: keeps.es.us-east-1.aws.found.io
  ELASTICSEARCH_INDEX: kontent-hml
  ELASTICSEARCH_PASS: Ztx2l2zLkf2DkmQYuAYRR0Ty
  ELASTICSEARCH_USER: elastic
  ELASTIC_APM_ENVIRONMENT: stage
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.found.io:443
  ENVIRONMENT: staging
  GOOGLE_APPLICATION_CREDENTIALS: /app/config/google_credentials/credential.json
  KEEPS_SECRET_TOKEN_INTEGRATION: ********************************
  KEYCLOAK_CLIENT_ID: kontent
  KEYCLOAK_CLIENT_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  KEYCLOAK_CLIENT_SECRET_KEY: 6128ac38-3fcf-4712-81e9-8e307d9bd849
  KEYCLOAK_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  KEYCLOAK_REALM: keeps-dev
  KEYCLOAK_SERVER_URL: http://keycloak-svc.security:8080/auth/
  PDFTRON_KEY: demo:1656284562355:7a77bd2f0300000000d2365bcd5c173dcc5f8131ef6fffb2abba5498e0
  SECRET_KEY: 5i%ul!b#rui^zipl!+u5nh0+7@j4wtu6o=!0_99t3axph$@oc=
  SLACK_LOG_CHANNEL_WEBHOOK: *****************************************************************************
  WEB_CONCURRENCY: "2"
  KONQUEST_CELERY_QUEUE: konquest-stage
  VIMEO_TOKEN: 5640b8610f102d368d22aa7c16880764
  GOOGLE_YOUTUBE_API_KEY: AIzaSyDAgRdNdk08YBZxizeCwcWluVCWcbAXnLM

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kontent
  namespace: stage
  labels:
    app: kontent
spec:
  selector:
    matchLabels:
      app: kontent
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: kontent
    spec:
      volumes:
        - name: kontent-secret-vol
          secret:
            secretName: kontent-secret
      containers:
        - name: kontent
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/kontent/kontent-server:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: kontent-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 512Mi
              cpu: 250m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "config.wsgi",
              "--bind",
              "0.0.0.0:8000",
            ]
            