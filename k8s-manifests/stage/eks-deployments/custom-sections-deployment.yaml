apiVersion: v1
kind: Service
metadata:
  name: custom-sections-svc
  namespace: stage
  labels:
    app: custom-sections
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  - name: grpc
    protocol: TCP
    port: 50051
    targetPort: 50051
  selector:
    app: custom-sections
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: custom-sections-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: stage
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_CLIENT_ID: custom-sections-microservice
  AUTH_CLIENT_SECRET: ********************************
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  AUTH_DEBUG: "false"
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_NAME: custom-sections_dev_db
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_DIALECT: postgres
  DB_DEBUG: "false"
  MIGRATIONS_RUN: "true"
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  KONQUEST_API_URL: http://konquest-svc:8000
  AUTH_APPLICATION_ID: ad7e5ad2-1552-43ab-a471-710954f0e66a
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt
  REDIS_PORT: "6379"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: custom-sections
  namespace: stage
  labels:
    app: custom-sections
spec:
  selector:
    matchLabels:
      app: custom-sections
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: custom-sections
    spec:
      volumes:
        - name: custom-sections-secret-vol
          secret:
            secretName: custom-sections-secret
      containers:
        - name: custom-sections
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-custom-sections:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: custom-sections-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 200m

