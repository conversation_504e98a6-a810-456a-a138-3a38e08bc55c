apiVersion: v1
kind: Service
metadata:
  name: keeps-kafka-connector-svc
  namespace: stage
  labels:
    app: keeps-kafka-connector
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8083
      targetPort: 8083
  selector:
    app: keeps-kafka-connector
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: keeps-kafka-connector-secret
  namespace: stage
type: Opaque
stringData:
  # shared
  ENV_SUFFIX: stage
  PG_HOSTNAME: postgresrdsdev
  PG_PORT: "5432"
  PG_USER: postgres
  PG_PASSWORD: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  PG_DBNAME_MYACCOUNT: myaccount_dev_db
  PG_DBNAME_KONQUEST: konquest_dev_db
  PG_DBNAME_KONTENT: kontent_dev_db
  PG_DBNAME_SMARTZAP: smartzap_dev_db
  PG_DBNAME_NOTIFICATION: notification_dev_db
  PG_DBNAME_REGULATORY_COMPLIANCE: regulatory_compliance_dev_db
  PG_DBNAME_INTEGRATION_GATEWAY_ALURA: integration_gateway_alura_dev_db
  PG_DBNAME_SISYPHUS: sisyphus_dev_db
  # keeps-indexer
  KAFKA_SERVERS: "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092"
  ELASTICSEARCH_SERVER: "https://keeps.es.us-east-1.aws.found.io:9243"
  ELASTICSEARCH_PASSWORD: "Ztx2l2zLkf2DkmQYuAYRR0Ty"
  ELASTICSEARCH_USER: "elastic"
  INDEXER_GROUP_ID: "keeps-indexer-stage"
  INDEXER_LOG_BATCH: "true"
  INDEXER_LOG_MESSAGE: "true"
  INDEXER_LOG_PAYLOAD: "true"
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.cloud.es.io
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_ENVIRONMENT: stage
  INDEXER_BATCH_MAX_SECONDS: "3"
  INDEXER_BATCH_MAX_SIZE: "100"
  INDEXER_POLL_TIMEOUT: "1"
  TRAIL_LEARNING_OBJECT_TYPE_ID: "d841e9d8-d669-4d88-9636-1072765d0738"
  MISSION_LEARNING_OBJECT_TYPE_ID: "798e50d7-8b97-4979-8728-4f9f1599bb05"
  # kafka-connectors
  CONNECTOR_VERSION: "14"
  LARGE_CONNECTOR_VERSION: "5"
  CONNECT_BOOTSTRAP_SERVERS: "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092"
  CONNECT_REST_ADVERTISED_HOST_NAME: "keeps-kafka-connector-svc"
  CONNECT_REST_PORT: "8083"
  CONNECT_GROUP_ID: "keeps-connect-stage"
  CONNECT_CONFIG_STORAGE_TOPIC: "_keeps-connect-configs-stage"
  CONNECT_OFFSET_STORAGE_TOPIC: "_keeps-connect-offsets-stage"
  CONNECT_STATUS_STORAGE_TOPIC: "_keeps-connect-status-stage"
  CONNECT_KEY_CONVERTER: "org.apache.kafka.connect.storage.StringConverter"
  CONNECT_VALUE_CONVERTER: "org.apache.kafka.connect.json.JsonConverter"
  CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: "false"
  CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
  CONNECT_REPLICATION_FACTOR: "1"
  CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: "1"
  CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: "1"
  CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: "1"
  SYNC_BATCH_SIZE: "1000"
  DEBEZIUM_BATCH_SIZE: "1024"
  DEBEZIUM_QUEUE_SIZE: "4098"
  INDEXER_REGULATORY_COMPLIANCE_GROUP_ID: "keeps-regulatory-compliance-indexer-stage"
  AWS_S3_REGION: "us-east-1"
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_KEY_ID: hLVvv9BKbgqa5O4CJ2x991MMF/e4Qo/zA33OZCeo  
  KAFKA_PRODUCER_MAX_REQUEST_SIZE: "5242880"
  KAFKA_PRODUCER_BUFFER_MEMORY: "8388608"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: keeps-kafka-connector
  namespace: stage
  labels:
    app: keeps-kafka-connector
spec:
  selector:
    matchLabels:
      app: keeps-kafka-connector
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: keeps-kafka-connector
    spec:
      volumes:
        - name: keeps-kafka-connector-secret-vol
          secret:
            secretName: keeps-kafka-connector-secret
      containers:
        - name: keeps-kafka-connector
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/keeps-connector:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8083
          envFrom:
            - secretRef:
                name: keeps-kafka-connector-secret
                optional: false
          resources:
            requests:
              memory: 2Gi
              cpu: "1"
            limits:
              memory: 3.5Gi
              cpu: "1.5"
