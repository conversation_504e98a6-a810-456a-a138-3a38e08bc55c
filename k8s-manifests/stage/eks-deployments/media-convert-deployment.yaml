---
apiVersion: v1
kind: ConfigMap
metadata:
  name: media-convert-config
  namespace: stage
  labels:
    app: media-convert
    environment: staging
data:
  # Application Configuration
  ENVIRONMENT: "staging"
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  FORCE_COLOR: "1"
  
  # Celery Configuration
  CELERY_APP: "app.celery_app"
  CELERY_LOGLEVEL: "info"
  CELERY_QUEUES: "media-convert.requests"
  CELERY_CONCURRENCY: "4"
  CELERY_MAX_TASKS_PER_CHILD: "1000"
  
  # RabbitMQ Configuration (using existing cluster services)
  RABBITMQ_HOST: "rabbitmq"
  RABBITMQ_PORT: "5672"
  RABBITMQ_USER: "user"
  RABBITMQ_VHOST: ""
  
  # Database Configuration (using existing cluster services)
  DATABASE_URL: "************************************************************************/media_convert_dev_db"
  DB_POOL_SIZE: "10"
  DB_MAX_OVERFLOW: "20"
  DB_POOL_RECYCLE: "3600"
  SQL_ECHO: "false"
  
  # AWS Configuration
  AWS_REGION: "us-east-1"
  AWS_S3_BUCKET: "keeps.kontent.media.hml"
  
  # Upload optimization
  MAX_UPLOAD_WORKERS: "4"
---
apiVersion: v1
kind: Secret
metadata:
  name: media-convert-secrets
  namespace: stage
  labels:
    app: media-convert
    environment: staging
type: Opaque
stringData:
  # AWS Credentials (base64 encoded)
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: K6glveg4RhH/matJ1Wm2s0L2Ab9EUuGsbymAfCLS
  
  # RabbitMQ Credentials
  RABBITMQ_PASSWORD: ptxom2d18ZLt

  # Database Credentials
  POSTGRES_PASSWORD: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  POSTGRES_USER: postgres
  POSTGRES_DB: media_convert_dev_db
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: media-convert
  namespace: stage
  labels:
    app: media-convert
    environment: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: media-convert
  template:
    metadata:
      labels:
        app: media-convert
        environment: staging
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        runAsNonRoot: true
      containers:
      - name: media-convert-worker
        image: 503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps/media-convert:stage
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: media-convert-config
        - secretRef:
            name: media-convert-secrets
        volumeMounts:
        - name: temp-storage
          mountPath: /tmp/media-convert
        - name: log-storage
          mountPath: /var/log/celery
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
            ephemeral-storage: "2Gi"
          limits:
            memory: "2Gi"
            cpu: "1500m"
            ephemeral-storage: "5Gi"
        livenessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "pgrep -f 'celery.*worker' > /dev/null || exit 1"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "pgrep -f 'celery.*worker' > /dev/null && test -d /tmp/media-convert || exit 1"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: temp-storage
        emptyDir:
          sizeLimit: 5Gi
      - name: log-storage
        emptyDir:
          sizeLimit: 1Gi
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - media-convert
              topologyKey: kubernetes.io/hostname
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: media-convert-hpa
  namespace: stage
  labels:
    app: media-convert
    environment: staging
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: media-convert
  minReplicas: 1
  maxReplicas: 2
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
