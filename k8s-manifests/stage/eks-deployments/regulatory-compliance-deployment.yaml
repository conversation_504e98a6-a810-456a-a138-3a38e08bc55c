apiVersion: v1
kind: Service
metadata:
  name: regulatory-compliance-svc
  namespace: stage
  labels:
    app: regulatory-compliance
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: regulatory-compliance
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: regulatory-compliance-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: development
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_CLIENT_ID: keeps-regulatory-compliance-api-stage
  AUTH_CLIENT_SECRET: ********************************
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_NAME: regulatory_compliance_dev_db
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  KONQUEST_URL: http://konquest-svc:8000
  KEEPS_SECRET_TOKEN_INTEGRATION: ********************************
  KONQUEST_APPLICATION_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288  
  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  USER_INFO_TIMEOUT_MS: "600000"
  MIGRATIONS_RUN: "true"
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  RABBITMQ_QUEUE: regulatory-compliance-tasks
  RABBITMQ_QUEUE_IS_DURABLE: "true"
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "********************************"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulatory-compliance
  namespace: stage
  labels:
    app: regulatory-compliance
spec:
  selector:
    matchLabels:
      app: regulatory-compliance
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: regulatory-compliance
    spec:
      volumes:
        - name: regulatory-compliance-secret-vol
          secret:
            secretName: regulatory-compliance-secret
      containers:
        - name: regulatory-compliance
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-regulatory-compliance:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: regulatory-compliance-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

