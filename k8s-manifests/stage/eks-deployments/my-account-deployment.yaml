apiVersion: v1
kind: Service
metadata:
  name: myaccount-svc
  namespace: stage
  labels:
    app: myaccount
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: myaccount
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: myaccount-secret
  namespace: stage
type: Opaque
stringData:
  HASH_SECRET_KEY: TxiGM7HNogTHnJsn
  AWS_BUCKET_NAME: keeps-media-stage
  AWS_BUCKET_PATH: myaccount
  AWS_BASE_CDN_URL: https://media-stage.keepsdev.com
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  AWS_MAIL_SENDER: "Plataforma Aprendizagem <<EMAIL>>"
  BROKER_URL: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_BROKER_URL: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_RESULT_BACKEND: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_RESULT_SERIALIZER: "json"
  CELERY_TASK_SERIALIZER: "json"
  CELERY_TIMEZONE: "America/Sao_Paulo"
  DATABASE_GAMEUP_NAME: "gameup_dev_db"
  DATABASE_HOST: postgresrdsdev
  DATABASE_KONQUEST_NAME: "konquest_dev_db"
  DATABASE_NAME: "myaccount_dev_db"
  DATABASE_PASSWORD: "1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg"
  DATABASE_PORT: "5432"
  DATABASE_SMARTZAP_NAME: "smartzap_dev_db"
  DATABASE_USER: "postgres"
  DEBOUNCE_API_KEY: "60d49f73e0093"
  DEBOUNCE_API_URL: "https://api.debounce.io/v1/"
  DEBUG: "True"
  ENVIRONMENT: "staging"
  IAM_ADMIN_PASS_ADMIN: "keeps011001"
  IAM_ADMIN_REALM: "keeps-dev"
  IAM_ADMIN_SERVER_URL: "http://keycloak-svc.security:8080/auth/"
  IAM_ADMIN_USER_ADMIN: "<EMAIL>"
  IAM_OPENID_CLIENT_ID: "myaccount-microservice"
  IAM_OPENID_PUBLIC_KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB"
  IAM_OPENID_REALM: "keeps-dev"
  IAM_OPENID_SECRET_KEY: DshvHe04bsAyUzdhCRpLiU3bGaeASwBs
  IAM_OPENID_SERVER_URL: "http://keycloak-svc.security:8080/auth/"
  IAM_OPENID_TOKEN_URL: "/protocol/openid-connect/token"
  KEYCLOAK_CLIENT_ID: "myaccount-microservice"
  KEYCLOAK_CLIENT_PUBLIC_KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB"
  KEYCLOAK_CLIENT_SECRET_KEY: "DshvHe04bsAyUzdhCRpLiU3bGaeASwBs"
  KEYCLOAK_REALM: "keeps-dev"
  KIZUP_API_URL: "http://kizup-api-stage.keepsdev.com/api/1/"
  KONQUEST_API_URL: "http://konquest-svc:8000/"
  KEEPS_SECRET_TOKEN_INTEGRATION: 637a2f9e72daba2ebb03a699c7a4c08d
  KONQUEST_WEB_URL: "https://konquest-stage.keepsdev.com/"
  MESSAGEBIRD_ACCESS_KEY: "*************************"
  MESSAGEBIRD_CHANNEL_ID: "442e50bac00848928b4a7f595ea6990c"
  MESSAGEBIRD_NAMESPACE: "6dadf5f1_35e6_4865_a6ff_be3535f8e460"
  MYACCOUNT_WEB_URL: "https://myaccount-stage.keepsdev.com/"
  SLACK_LOG_CHANNEL_WEBHOOK: "*****************************************************************************"
  SMARTZAP_WEB_URL: "https://smartzap-stage.keepsdev.com/"
  TWILIO_ACCOUNT_SID: "ACb530962b6a3fdea6990e63792eae0a8000"
  TWILIO_AUTH_TOKEN: "3ce181845e6faa49685b7cdb81fe18a1"
  TWILIO_SUPPORT_PHONE_NUMBER: "+***********"
  WHATSAPP_BROKER: "twilio"
  SUSPEND_SIGNALS: "False"
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.cloud.es.io
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_ENVIRONMENT: stage
  DATABASE_KEYCLOAK_NAME: keycloak_db
  FORCE_HTTPS_IN_FORWARDED_HOST: "true"
  X_FORWARDED_HOST: learning-platform-api-stage.keepsdev.com/myaccount
  PYTHON_PATH: /usr/src/app/
  # AFKA_SERVERS: b-1.kafka-learning-platfor.7h2qni.c8.kafka.us-east-1.amazonaws.com:9092,b-2.kafka-learning-platfor.7h2qni.c8.kafka.us-east-1.amazonaws.com:9092
  KAFKA_SERVERS: "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092"
  WORKSPACE_TOPICS: '[{"workspace_id": "b8c7b36b-3811-4a18-a8f1-6544a4128578", "topic_key": "users_by_s3_csv_file_stage_test.caixa_test_stage", "role_id": "********-5e4e-48c6-91d7-dbeb360c7205"}, {"workspace_id": "e76b5082-f4fe-4f41-be79-1977840e16a8", "topic_key": "users_by_s3_csv_file_stage_test.keeps"}]'


---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myaccount
  namespace: stage
  labels:
    app: myaccount
spec:
  selector:
    matchLabels:
      app: myaccount
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: myaccount
    spec:
      volumes:
        - name: myaccount-secret-vol
          secret:
            secretName: myaccount-secret
      containers:
        - name: myaccount
          image: "************.dkr.ecr.us-east-1.amazonaws.com/account/my-account-server:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: myaccount-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "config.wsgi",
              "--bind",
              "0.0.0.0:8000",
            ]
