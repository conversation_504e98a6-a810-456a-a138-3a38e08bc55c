apiVersion: v1
kind: Service
metadata:
  name: corp-svc
  namespace: stage
  labels:
    app: corp
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: corp
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: corp-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: development
  KONQUEST_URL: http://konquest-svc:8000
  MYACCOUNT_URL: http://myaccount-svc:8000
  MYACCOUNT_V2_URL: http://myaccount-v2-svc:3000/api
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_CLIENT_ID: keeps-corp-api-stage
  AUTH_CLIENT_SECRET: Emzwb36eRAT52Aiz0EltFeUgaaEwzyKj
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: corp
  namespace: stage
  labels:
    app: corp
spec:
  selector:
    matchLabels:
      app: corp
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: corp
    spec:
      volumes:
        - name: corp-secret-vol
          secret:
            secretName: corp-secret
      containers:
        - name: corp
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps-corp:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: corp-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 300m

