apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: stage
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7.2.4
          ports:
            - containerPort: 6379
          args: ["redis-server", "--requirepass", "ptxom2d18ZLt"]

---
kind: Service
apiVersion: v1
metadata:
  name: redis-svc
  namespace: stage
spec:
  # type: LoadBalancer // descomentar essa linha para acessar externamente diretamente pelo LB
  selector:
    app: redis
  ports:
    - name: p1
      protocol: TCP
      port: 6379
      targetPort: 6379
