apiVersion: v1
kind: Service
metadata:
  name: certificate-manager-svc
  namespace: stage
  labels:
    app: certificate-manager
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000
    - name: grpc
      protocol: TCP
      port: 50051
      targetPort: 50051
  selector:
    app: certificate-manager
  type: ClusterIP

---
apiVersion: v1
kind: Secret
metadata:
  name: certificate-manager-secret
  namespace: stage
type: Opaque
stringData:
  NODE_ENV: stage
  I18N_PATH: assets/i18n
  TEMPLATES_PATH: assets/templates
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  AUTH_CLIENT_ID: keeps-certificate-manager-api-stage
  AUTH_CLIENT_SECRET: ********************************
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_NAME: certificate_manager_dev_db
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_DIALECT: postgres
  MIGRATIONS_RUN: "true"
  KONQUEST_APPLICATION_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "637a2f9e72daba2ebb03a699c7a4c08d"
  AWS_S3_ACCESS_KEY_ID: ********************
  AWS_S3_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  AWS_S3_REGION: "us-east-1"
  AWS_S3_BUCKET: keeps-media-stage
  AWS_S3_BUCKET_PATH: certificate-manager
  AWS_S3_CDN_URL: https://media-stage.keepsdev.com


---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: certificate-manager
  namespace: stage
  labels:
    app: certificate-manager
spec:
  selector:
    matchLabels:
      app: certificate-manager
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: certificate-manager
    spec:
      volumes:
        - name: certificate-manager-secret-vol
          secret:
            secretName: certificate-manager-secret
      containers:
        - name: certificate-manager
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-certificate-manager:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: certificate-manager-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1000m
          livenessProbe:
            tcpSocket:
              port: 50051
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: 50051
            initialDelaySeconds: 3
            periodSeconds: 5
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            allowPrivilegeEscalation: false

