apiVersion: v1
kind: Service
metadata:
  name: myaccount-v2-svc
  namespace: stage
  labels:
    app: myaccount-v2
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000
    - name: grpc
      protocol: TCP
      port: 50051
      targetPort: 50051
  selector:
    app: myaccount-v2
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: myaccount-v2-secret
  namespace: stage
type: Opaque
stringData:
  CRYPTO_SECRET_KEY: TxiGM7HNogTHnJsn
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps-dev
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  AUTH_CLIENT_ID: myaccount-microservice
  AUTH_CLIENT_SECRET: DshvHe04bsAyUzdhCRpLiU3bGaeASwBs
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  RABBITMQ_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  DB_HOST: postgresrdsdev
  DB_PORT: "5432"
  DB_NAME: myaccount_dev_db
  DB_USER: postgres
  DB_PASS: 1s5kJTH9J9L8UW85Tvdk4IXYT8mYRg
  DB_DIALECT: postgres
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt
  KAFKA_TOPIC_SUFFIX: stage
  NOTIFICATION_API_URL: http://notification-svc:3000
  KAFKA_SERVERS: "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092"
  MYACCOUNT_ID: ad7e5ad2-1552-43ab-a471-710954f0e66a
  KONQUEST_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  SMARTZAP_ADMIN_ROLE: 3d010792-7119-4e14-bea3-5258a31f1ddc
  I18N_PATH: assets/i18n
  KONQUEST_WEB_URL: https://konquest-stage.keepsdev.com
  SMARTZAP_WEB_URL: https://smartzap-stage.keepsdev.com
  AWS_S3_ACCESS_KEY_ID: ********************
  AWS_S3_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  AWS_S3_REGION: us-east-1
  AWS_S3_BUCKET: keeps-media-stage
  AWS_S3_BUCKET_PATH: myaccount
  AWS_S3_CDN_URL: https://media-stage.keepsdev.com
  GAMIFICATION_API_URL: http://gamification-svc:3000
  QUEUE_SMARTZAP_COMPANY: smartzap-companies-stage
  MYACCOUNT_WEB_URL: https://myaccount-stage.keepsdev.com

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myaccount-v2
  namespace: stage
  labels:
    app: myaccount-v2
spec:
  selector:
    matchLabels:
      app: myaccount-v2
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: myaccount-v2
    spec:
      volumes:
        - name: myaccount-v2-secret-vol
          secret:
            secretName: myaccount-v2-secret
      containers:
        - name: myaccount-v2
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-myaccount-v2:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: myaccount-v2-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m