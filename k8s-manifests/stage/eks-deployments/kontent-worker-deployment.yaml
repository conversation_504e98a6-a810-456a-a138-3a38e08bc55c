apiVersion: apps/v1
kind: Deployment
metadata:
  name: kontent-worker
  namespace: stage
  labels:
    app: kontent-worker
spec:
  selector:
    matchLabels:
      app: kontent-worker
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: kontent-worker
    spec:
      volumes:
        - name: kontent-secret-vol
          secret:
            secretName: kontent-secret
      containers:
        - name: kontent-worker
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/kontent/kontent-server:stage"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: kontent-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: "1"
          command:
            [
              "/usr/bin/supervisord",
              "-c",
              "/etc/supervisord.conf"
            ]