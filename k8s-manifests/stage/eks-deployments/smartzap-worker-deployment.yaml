apiVersion: apps/v1
kind: Deployment
metadata:
  name: smartzap-worker
  namespace: stage
  labels:
    app: smartzap-worker
spec:
  selector:
    matchLabels:
      app: smartzap-worker
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: smartzap-worker
    spec:
      volumes:
        - name: smartzap-secret-vol
          secret:
            secretName: smartzap-secret
      containers:
        - name: smartzap-worker
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/smart-zap/smart-zap-server:stage"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: smartzap-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 512Mi
              cpu: 500m
          command:
            [
              "/usr/bin/supervisord",
              "-c",
              "/etc/supervisord.conf"
            ]