apiVersion: v1
kind: Service
metadata:
  name: konquest-svc
  namespace: stage
  labels:
    app: konquest
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: konquest
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: konquest-secret
  namespace: stage
type: Opaque
stringData:
  DEBUG: "True"
  LOG_LEVEL: error
  AWS_BUCKET_NAME: keeps-media-stage
  AWS_COMPREHEND_ACCESS_KEY_ID: ********************
  AWS_COMPREHEND_SECRET_ACCESS_KEY: Mdet4eD0/ru7+DGAsqacJx/NjEp8qmf8H4FBWj75
  AWS_LAMBDA_ACCESS_KEY_ID: ********************
  AWS_LAMBDA_REGION_NAME: us-east-1
  AWS_LAMBDA_SECRET_ACCESS_KEY: RhLUlAQ+rDbAOkaXIG3iGfiLQ4zh0TIGiY/z402z
  AWS_MAIL_SENDER: Plataforma Aprendizagem <<EMAIL>>
  BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_NAMESPACE: CELERY
  CELERY_QUEUE: konquest-stage
  CELERY_RESULT_BACKEND: celery_amqp_backend.AMQPBackend://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_USER_INTEGRATION_NUM_PROCS: "2"
  CERTIFICATE_PASSWORD: Vitoria100
  CERTIFICATE_MANAGER_GRPC_URL: certificate-manager-svc:50051
  DATABASE_HOST: pgbouncer-svc
  DATABASE_NAME: konquest_db
  DATABASE_PASSWORD: 1cc4b910cfc1bd45cd3f9628992d447e
  DATABASE_PORT: "5432"
  DATABASE_USER: postgres
  DATABASE_AUTOCOMMIT: "True"
  DISABLE_EXPIRED_LEARNING_TRAILS_CRONTAB: "*/5 * * * *"
  ELASTIC_APM_ENVIRONMENT: stage
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.found.io:443
  ENVIRONMENT: staging
  KEYCLOAK_CLIENT_ID: konquest-microservice
  KEYCLOAK_CLIENT_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  KEYCLOAK_CLIENT_SECRET_KEY: HmBiifoysUrfVTAOgEWCJ2LAfz4arcY0
  KEYCLOAK_REALM: keeps-dev
  KONTENT_API_URL: http://kontent-svc:8000
  MYACCOUNT_API_URL: http://myaccount-svc:8000
  MYACCOUNT_V2_API_URL: http://myaccount-v2-svc:3000
  SLACK_LOG_CHANNEL_WEBHOOK: *****************************************************************************
  REDIS_LOCATION: redis://redis-svc:6379
  REDIS_PASSWORD: ptxom2d18ZLt
  USER_INFO_TIMEOUT: "1800"
  FORCE_HTTPS_IN_FORWARDED_HOST: "true"
  X_FORWARDED_HOST: learning-platform-api-stage.keepsdev.com/konquest
  KONQUEST_WEB_MISSION_DETAIL_URL: https://konquest-stage.keepsdev.com/C/{}
  REGULATORY_COMPLIANCE_QUEUE: regulatory-compliance-tasks
  REGULATORY_COMPLIANCE_EXCHANGE: integrations
  REGULATORY_COMPLIANCE_EXCHANGE_TYPE: direct
  WEB_CONCURRENCY: "3"
  WORKER_CONCURRENCY: "2"
  DATABASE_CONN_MAX_AGE: "30"
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: konquest
  namespace: stage
  labels:
    app: konquest
spec:
  selector:
    matchLabels:
      app: konquest
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: konquest
    spec:
      volumes:
        - name: konquest-secret-vol
          secret:
            secretName: konquest-secret
      containers:
        - name: konquest
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/konquest/konquest-server:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: konquest-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "config.wsgi",
              "--bind",
              "0.0.0.0:8000",
            ]
