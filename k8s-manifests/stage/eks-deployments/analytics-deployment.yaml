apiVersion: v1
kind: Service
metadata:
  name: analytics-svc
  namespace: stage
  labels:
    app: analytics
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: analytics
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: analytics-secret
  namespace: stage
type: Opaque
stringData:
  ENVIRONMENT: staging
  AWS_BASE_S3_URL: "https://s3.amazonaws.com"
  AWS_BUCKET_NAME: keeps-media-stage
  AWS_BUCKET_NAME_REPORT: keeps-media-stage
  AWS_LAMBDA_ACCESS_KEY_ID: "********************"
  AWS_LAMBDA_REGION_NAME: "us-east-1"
  AWS_LAMBDA_SECRET_ACCESS_KEY: "RhLUlAQ+rDbAOkaXIG3iGfiLQ4zh0TIGiY/z402z"
  AWS_S3_ACCESS_KEY_ID: "********************"
  AWS_S3_REGION_NAME: "us-east-1"
  AWS_S3_SECRET_ACCESS_KEY: "TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf"
  CELERY_BROKER_URL: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_RESULT_BACKEND: "rpc://user:ptxom2d18ZLt@rabbitmq:5672"
  DATABASE_KONQUEST_URL: "************************************************************************/konquest_dev_db"
  DATABASE_KONTENT_URL: "************************************************************************/kontent_dev_db"
  DATABASE_MYACCOUNT_URL: "************************************************************************/myaccount_dev_db"
  DATABASE_SMARTZAP_URL: "************************************************************************/smartzap_dev_db"
  DATABASE_URL: "************************************************************************/analytics_dev_db"
  ELASTICSEARCH_AUTH: "elastic:Ztx2l2zLkf2DkmQYuAYRR0Ty"
  ELASTICSEARCH_INDEX_ACTIVITIES: "kafka-analytics-activities-stage"
  ELASTICSEARCH_INDEX_ANSWERS: "kafka-analytics-answers-stage"
  ELASTICSEARCH_INDEX_CHANNELS: "kafka-analytics-channels-stage"
  ELASTICSEARCH_INDEX_COURSES: "kafka-analytics-courses-stage"
  ELASTICSEARCH_INDEX_COURSES_V1: "learning-analytics-courses-stage"
  ELASTICSEARCH_INDEX_COURSES_V2: "kafka-analytics-courses-stage"
  ELASTICSEARCH_INDEX_COURSE_EVALUATIONS: "kafka-analytics-course-evaluations-stage"
  ELASTICSEARCH_INDEX_COURSE_RATINGS: "kafka-analytics-course-ratings-stage"
  ELASTICSEARCH_INDEX_ENROLLMENTS: "kafka-analytics-enrollments-stage"
  ELASTICSEARCH_INDEX_PULSES: "kafka-analytics-pulses-stage"
  ELASTICSEARCH_INDEX_USERS: "kafka-analytics-users-stage"
  ELASTICSEARCH_INDEX_USERS_V1: "learning-analytics-users-stage"
  ELASTICSEARCH_INDEX_USERS_V2: "kafka-analytics-users-stage"
  ELASTICSEARCH_URL: "https://keeps.es.us-east-1.aws.found.io"
  KEEPS_SECRET_TOKEN_INTEGRATION: "637a2f9e72daba2ebb03a699c7a4c08d"
  KEYCLOAK_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
  KEYCLOAK_ISS: https://iam.keepsdev.com/auth/realms/keeps-dev
  KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS: "365"
  SLACK_LOG_CHANNEL_WEBHOOK: "*******************************************************************************"
  WEB_CONCURRENCY: "1"
  WORKER_CONCURRENCY: "2"
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.cloud.es.io
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_ENVIRONMENT: stage
  AWS_BASE_CDN_URL: "https://media-stage.keepsdev.com"
  AWS_BUCKET_PATH: analytics
  JASPER_REPORT_SERVER_URL: "http://report-generator-svc:8080"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics
  namespace: stage
  labels:
    app: analytics
spec:
  selector:
    matchLabels:
      app: analytics
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: analytics
    spec:
      volumes:
        - name: analytics-secret-vol
          secret:
            secretName: analytics-secret
      containers:
        - name: analytics
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learn-analytics/learn-analytics-server:stage"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: analytics-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "wsgi:app",
              "--bind",
              "0.0.0.0:8000",
            ]
