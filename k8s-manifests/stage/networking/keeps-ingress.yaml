apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-corp-stage
  namespace: stage
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  ingressClassName: nginx
  rules:
    - host: corp-api-stage.keepsdev.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: corp-svc
                port:
                  number: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-stage
  namespace: stage
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  rules:
    - host: learning-platform-api-stage.keepsdev.com
      http:
        paths:
          - path: /search(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: search-svc
                port:
                  number: 3000
          - path: /notification(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: notification-svc
                port:
                  number: 3000
          - path: /myaccount(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: myaccount-svc
                port:
                  number: 8000
          - path: /analytics(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: analytics-svc
                port:
                  number: 8000
          - path: /kontent(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: kontent-svc
                port:
                  number: 8000
          - path: /smartzap(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: smartzap-svc
                port:
                  number: 8000

          - path: /smartzap-caixa(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: smartzap-caixa-svc
                port:
                  number: 8000

          - path: /konquest(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: konquest-svc
                port:
                  number: 8000
          - path: /regulatory-compliance(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: regulatory-compliance-svc
                port:
                  number: 3000
          - path: /sisyphus(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: sisyphus-svc
                port:
                  number: 3000
          - path: /teams(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: bot-teams-svc
                port:
                  number: 3978
          - path: /slack(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: bot-slack-svc
                port:
                  number: 3978
          - path: /report-generator(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: report-generator-svc
                port:
                  number: 8080
          - path: /myaccount-v2(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: myaccount-v2-svc
                port:
                  number: 3000
          - path: /integration-gateway-alura(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: integration-gateway-alura-svc
                port:
                  number: 3000
          - path: /analytics-ai(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: analytics-ai-svc
                port:
                  number: 8000            
          - path: /certificate-manager(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: certificate-manager-svc
                port:
                  number: 3000     
          - path: /custom-sections(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: custom-sections-svc
                port:
                  number: 3000     