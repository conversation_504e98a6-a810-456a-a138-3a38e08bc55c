apiVersion: v1
kind: Service
metadata:
  name: user-registration-svc
  namespace: production
  labels:
    app: user-registration
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: user-registration
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: user-registration-secret
  namespace: production
type: Opaque
stringData:
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_NAME: myaccount_db
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-registration
  namespace: production
  labels:
    app: user-registration
spec:
  selector:
    matchLabels:
      app: user-registration
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: user-registration
    spec:
      volumes:
        - name: user-registration-secret-vol
          secret:
            secretName: user-registration-secret
      containers:
        - name: user-registration
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-user-registration:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: user-registration-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 128Mi
              cpu: 300m
