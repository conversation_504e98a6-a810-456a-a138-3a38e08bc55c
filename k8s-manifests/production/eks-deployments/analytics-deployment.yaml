apiVersion: v1
kind: Service
metadata:
  name: analytics-svc
  namespace: production
  labels:
    app: analytics
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: analytics
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: analytics-secret
  namespace: production
type: Opaque
stringData:
  DISCORD_WEBHOOK: https://discord.com/api/webhooks/1079824862719262791/rMpYDKtZT0_Yp5_wgNIgfK4Qgbg98TbiNbdYIAkkNYKr32EcFewq7Tw9JkQZlfJY3P1Y
  AWS_BASE_S3_URL: https://s3.amazonaws.com
  AWS_BUCKET_NAME: keeps-media
  AWS_BUCKET_NAME_REPORT: keeps-media
  AWS_LAMBDA_ACCESS_KEY_ID: ********************
  AWS_LAMBDA_REGION_NAME: us-east-1
  AWS_LAMBDA_SECRET_ACCESS_KEY: RhLUlAQ+rDbAOkaXIG3iGfiLQ4zh0TIGiY/z402z
  AWS_S3_ACCESS_KEY_ID: "********************"
  AWS_S3_REGION_NAME: us-east-1
  AWS_S3_SECRET_ACCESS_KEY: "TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf"
  CELERY_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_DEFAULT_QUEUE: analytics-tasks
  CELERY_RESULT_BACKEND: rpc://user:ptxom2d18ZLt@rabbitmq:5672
  DATABASE_KONQUEST_URL: "*********************************************************************************************/konquest_db"
  DATABASE_KONTENT_URL: "*********************************************************************************************/kontent_db"
  DATABASE_MYACCOUNT_URL: "*********************************************************************************************/myaccount_db"
  DATABASE_SMARTZAP_URL: "*********************************************************************************************/smartzap_db"
  DATABASE_URL: "*********************************************************************************************/analytics_db"
  ELASTICSEARCH_AUTH: elastic:Ztx2l2zLkf2DkmQYuAYRR0Ty
  ELASTICSEARCH_INDEX_ACTIVITIES: "kafka-analytics-activities-production"
  ELASTICSEARCH_INDEX_ANSWERS: "kafka-analytics-answers-production"
  ELASTICSEARCH_INDEX_CHANNELS: "kafka-analytics-channels-production"
  ELASTICSEARCH_INDEX_COURSES: "kafka-analytics-courses-production"
  ELASTICSEARCH_INDEX_COURSES_V1: "learning-analytics-courses"
  ELASTICSEARCH_INDEX_COURSES_V2: "kafka-analytics-courses-production"
  ELASTICSEARCH_INDEX_COURSE_EVALUATIONS: "kafka-analytics-course-evaluations-production"
  ELASTICSEARCH_INDEX_COURSE_RATINGS: "kafka-analytics-course-ratings-production"
  ELASTICSEARCH_INDEX_ENROLLMENTS: "kafka-analytics-enrollments-production"
  ELASTICSEARCH_INDEX_PULSES: "kafka-analytics-pulses-production"
  ELASTICSEARCH_INDEX_USERS: "kafka-analytics-users-production"
  ELASTICSEARCH_INDEX_USERS_V1: "learning-analytics-users"
  ELASTICSEARCH_INDEX_USERS_V2: "kafka-analytics-users-production"
  ELASTICSEARCH_URL: https://keeps.es.us-east-1.aws.found.io
  ENVIRONMENT: production
  KEYCLOAK_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  KEYCLOAK_ISS: https://iam.keepsdev.com/auth/realms/keeps
  KEEPS_SECRET_TOKEN_INTEGRATION: 637a2f9e72daba2ebb03a699c7a4c08d
  KONQUEST_EXPORT_GROUPS_MISSIONS_USERS_LIMIT_DAYS: "730"
  KONQUEST_EXPORT_MISSIONS_LIMIT_DAYS: "3653"
  KONQUEST_EXPORT_MISSIONS_QUIZZES_ANSWERS_LIMIT_DAYS: "365"
  KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS: "1800"
  KONQUEST_EXPORT_PULSES_CHANNELS_LIMIT_DAYS: "1500"
  KONQUEST_EXPORT_PULSES_QUIZZES_ANSWERS_LIMIT_DAYS: "365"
  KONQUEST_EXPORT_USERS_PULSES_ACTIVITIES_LIMIT_DAYS: "60"
  KONQUEST_EXPORT_USER_CONSUMPTION_LIMIT_DAYS: "365"
  WEB_CONCURRENCY: "1"
  WORKER_CONCURRENCY: "5"
  LOG_LEVEL: "error"
  AWS_BASE_CDN_URL: "https://media.keepsdev.com"
  AWS_BUCKET_PATH: analytics
  JASPER_REPORT_SERVER_URL: "http://report-generator-svc:8080"
  KONQUEST_EXPORT_GROUPS_CHANNELS_USERS_LIMIT_DAYS: "1800"
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics
  namespace: production
  labels:
    app: analytics
spec:
  selector:
    matchLabels:
      app: analytics
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: analytics
    spec:
      volumes:
        - name: analytics-secret-vol
          secret:
            secretName: analytics-secret
      containers:
        - name: analytics
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learn-analytics/learn-analytics-server:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: analytics-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "wsgi:app",
              "--bind",
              "0.0.0.0:8000",
            ]
