apiVersion: v1
kind: Service
metadata:
  name: smartzap-caixa-svc
  namespace: production
  labels:
    app: smartzap-caixa
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: smartzap-caixa
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: smartzap-caixa-secret
  namespace: production
type: Opaque
stringData:
  LOG_LEVEL: error
  DISCORD_WEBHOOK: https://discord.com/api/webhooks/1088908421560008816/qUDb53fNtngeS6I3RYL3vpFs9-I4wJFD48PpnPdRCqlzq8S9TKD3_JOsCe0DGnNhKNwm
  AWS_ACCESS_KEY_ID: ********************
  AWS_BASE_S3_URL: https://s3.amazonaws.com
  AWS_BUCKET_NAME: keeps-smartzap-medias
  AWS_LAMBDA_ACCESS_KEY_ID: ********************
  AWS_LAMBDA_REGION_NAME: us-east-1
  AWS_LAMBDA_SECRET_ACCESS_KEY: RhLUlAQ+rDbAOkaXIG3iGfiLQ4zh0TIGiY/z402z
  AWS_REGION_NAME: us-east-1
  AWS_SECRET_ACCESS_KEY: TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf
  CELERY_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_QUEUE: smartzap
  CHATTERBOT_INTERVAL_MINUTES: "5"
  COURSE_PROCESSOR_INTERVAL_MINUTES: "5"
  DATABASE_URL: *********************************************************************************************/smartzap_db
  DEFAULT_IMAGE_DISCLAIMER: https://s3.amazonaws.com/keeps-smartzap-medias/view-assets/disclaimer_pr_BR.jpg
  DEFAULT_IMAGE_END_COURSE: https://s3.amazonaws.com/keeps-smartzap-medias/view-assets/congratulations_pt_BR.jpg
  DEFAULT_IMAGE_LESSON: https://s3.amazonaws.com/keeps-smartzap-medias/view-assets/new_lession_pt_BR.jpg
  ELASTIC_APM_ENVIRONMENT: production
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.found.io:443
  ENROLLMENT_LIMIT_DAYS_WAITING: "30"
  ENVIRONMENT: production
  GCM_FIREBASE_KEY: AIzaSyDTTd5FpDZyHF86-EYgWDyi2POuaEUOVHQ
  GCM_FIREBASE_URL: https://firebasedynamiclinks.googleapis.com
  KEEPS_SECRET_TOKEN_INTEGRATION: 637a2f9e72daba2ebb03a699c7a4c08d
  KEYCLOAK_PASS_ADMIN: keeps011001
  KEYCLOAK_REALM: keeps
  KEYCLOAK_SERVER_URL: http://keycloak-svc.security:8080/auth/
  KEYCLOAK_USER_ADMIN: <EMAIL>
  KONTENT_DATABASE_URL: *********************************************************************************************/kontent_db
  KONTENT_URL: http://kontent-svc:8000
  LEARN_ANALYTICS_URL: http://analytics-svc:8000/api/v1/
  MYACC_URL: http://myaccount-svc:8000
  MYACC_V2_URL: http://myaccount-v2-svc:3000
  
  SCHEDULER_INTERVAL_MINUTES: "5"
  SCHEDULE_CALLBACK_URL: https://learning-platform-api.keepsdev.com/smartzap
  SLACK_LOG_CHANNEL_WEBHOOK: *******************************************************************************
  SMARTVIEW_URL: https://smartview.keepsdev.com/contents
  SMS_FIRE_KEY: a2VlcHNkZXY6S2VlcHMwMTEwMDFA
  SMS_FIRE_TOKEN_WHATSAPP: 25a2fb618f39ef2f5e2448b7a1fad0cb
  SMS_FIRE_URL: https://api.smsfire.com.br/v1/
  TWILIO_ACCOUNT_SID: **********************************
  TWILIO_AUTH_TOKEN: 3ce181845e6faa49685b7cdb81fe18a1
  TWILIO_CHANNELS: '{"pt-BR":"**********************************:3ce181845e6faa49685b7cdb81fe18a1@+************","es":"**********************************:3ce181845e6faa49685b7cdb81fe18a1@+************"}' 
  USER_TOKEN_EXPIRATION: "5"
  WEB_CONCURRENCY: "2"
  WORKER_TIMEOUT: "600"
  AWS_CDN_BASE_URL: https://media.keepsdev.com
  KEYCLOAK_CLIENT_ID: smartzap-microservice
  KEYCLOAK_CLIENT_SECRET: 050e7a65-d8b9-4077-9d06-60f442bf86f2
  CAIXA_WORKSPACE_IDS: 4576514f-6630-4847-ad6b-04923c0f8556,df2d246e-62c7-448f-8dac-6c265f5f0081,e76b5082-f4fe-4f41-be79-1977840e16a8

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: smartzap-caixa
  namespace: production
  labels:
    app: smartzap-caixa
spec:
  selector:
    matchLabels:
      app: smartzap-caixa
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: smartzap-caixa
    spec:
      volumes:
        - name: smartzap-caixa-secret-vol
          secret:
            secretName: smartzap-caixa-secret
      containers:
        - name: smartzap-caixa
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/smart-zap/smart-zap-server:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: smartzap-caixa-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 50m
            limits:
              memory: 256Mi
              cpu: 250m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--log-level=error",   
              "--config",
              "gunicorn_config.py",
              "caixa-app",
              "--bind",
              "0.0.0.0:8000",
            ]
