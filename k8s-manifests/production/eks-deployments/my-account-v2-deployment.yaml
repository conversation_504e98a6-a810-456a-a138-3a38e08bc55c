apiVersion: v1
kind: Service
metadata:
  name: myaccount-v2-svc
  namespace: production
  labels:
    app: myaccount-v2
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000
    - name: grpc
      protocol: TCP
      port: 50051
      targetPort: 50051
  selector:
    app: myaccount-v2
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: myaccount-v2-secret
  namespace: production
type: Opaque
stringData:
  CRYPTO_SECRET_KEY: RVT5DJqpPiWGiQ6q
  AUTH_DEBUG: "false"
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  AUTH_CLIENT_ID: myaccount
  AUTH_CLIENT_SECRET: efe89a2a-07cc-436c-9d41-dc1fb72c8f6b
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  RABBITMQ_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  DB_NAME: myaccount_db
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_DIALECT: postgres
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt
  KAFKA_TOPIC_SUFFIX: production
  NOTIFICATION_API_URL: http://notification-svc:3000
  KAFKA_SERVERS: "b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092"
  MYACCOUNT_ID: ad7e5ad2-1552-43ab-a471-710954f0e66a
  KONQUEST_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  SMARTZAP_ADMIN_ROLE: 3d010792-7119-4e14-bea3-5258a31f1ddc
  I18N_PATH: assets/i18n
  KONQUEST_WEB_URL: https://konquest.keepsdev.com
  SMARTZAP_WEB_URL: https://smartzap.keepsdev.com
  AWS_S3_ACCESS_KEY_ID: ********************
  AWS_S3_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  AWS_S3_REGION: us-east-1
  AWS_S3_BUCKET: keeps-media
  AWS_S3_BUCKET_PATH: myaccount
  AWS_S3_CDN_URL: https://media.keepsdev.com
  MYACCOUNT_WEB_URL: https://myaccount.keepsdev.com
  GAMIFICATION_API_URL: http://gamification-svc:3000
  QUEUE_SMARTZAP_COMPANY: smartzap-companies

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myaccount-v2
  namespace: production
  labels:
    app: myaccount-v2
spec:
  selector:
    matchLabels:
      app: myaccount-v2
  revisionHistoryLimit: 1
  replicas: 4
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: myaccount-v2
    spec:
      volumes:
        - name: myaccount-v2-secret-vol
          secret:
            secretName: myaccount-v2-secret
      containers:
        - name: myaccount-v2
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-myaccount-v2:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: myaccount-v2-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 500m