apiVersion: v1
kind: Service
metadata:
  name: bot-slack-svc
  namespace: production
  labels:
    app: bot-slack
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3978
      targetPort: 3978
  selector:
    app: bot-slack
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: bot-slack-secret
  namespace: production
type: Opaque
stringData:
  SLACK_CLIENT_ID: "489706589072.7172228594915"
  SLACK_CLIENT_SECRET: 8a281c96a212c6d0f76c32674ed780e9
  SLACK_SIGNING_SECRET: 8c88a5775548daa2eeaa0e571c840983
  SLACK_OAUTH_REDIRECT_URL: https://learning-platform-api.keepsdev.com/slack/slack/oauth_redirect
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  DB_NAME: bot_slack_db
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bot-slack
  namespace: production
  labels:
    app: bot-slack
spec:
  selector:
    matchLabels:
      app: bot-slack
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: bot-slack
    spec:
      volumes:
        - name: bot-slack-secret-vol
          secret:
            secretName: bot-slack-secret
      containers:
        - name: bot-slack
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps-bot-slack:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3978
          envFrom:
            - secretRef:
                name: bot-slack-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 128Mi
              cpu: 100m