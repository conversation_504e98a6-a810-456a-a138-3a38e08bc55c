apiVersion: v1
kind: Service
metadata:
  name: keeps-kafka-connector-svc
  namespace: production
  labels:
    app: keeps-kafka-connector
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8083
      targetPort: 8083
  selector:
    app: keeps-kafka-connector
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: keeps-kafka-connector-secret
  namespace: production
type: Opaque
stringData:
  # Connector
  ENV_SUFFIX: production
  CONNECTOR_VERSION: "5"
  LARGE_CONNECTOR_VERSION: "0"
  CONNECT_BOOTSTRAP_SERVERS: b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092
  CONNECT_REST_ADVERTISED_HOST_NAME: "keeps-kafka-connector-svc"
  CONNECT_REST_PORT: "8083"
  CONNECT_GROUP_ID: keeps-connect-production
  CONNECT_CONFIG_STORAGE_TOPIC: _keeps-connect-configs-production
  CONNECT_OFFSET_STORAGE_TOPIC: _keeps-connect-offsets-production
  CONNECT_STATUS_STORAGE_TOPIC: _keeps-connect-status-production
  CONNECT_KEY_CONVERTER: org.apache.kafka.connect.storage.StringConverter
  CONNECT_VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
  CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: "false"
  CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
  CONNECT_REPLICATION_FACTOR: "2"
  CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: "2"
  CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: "2"
  CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: "2"
  SYNC_BATCH_SIZE: "1000"
  DEBEZIUM_BATCH_SIZE: "1024"
  DEBEZIUM_QUEUE_SIZE: "4098"
  AWS_S3_REGION: "us-east-1"
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_KEY_ID: hLVvv9BKbgqa5O4CJ2x991MMF/e4Qo/zA33OZCeo

  # Indexer
  KAFKA_SERVERS: b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092
  ELASTICSEARCH_SERVER: https://keeps.es.us-east-1.aws.found.io:9243
  ELASTICSEARCH_USER: elastic
  ELASTICSEARCH_PASSWORD: Ztx2l2zLkf2DkmQYuAYRR0Ty
  INDEXER_GROUP_ID: keeps-indexer-production
  INDEXER_POLL_TIMEOUT: "1"
  INDEXER_BATCH_MAX_SECONDS: "3"
  INDEXER_BATCH_MAX_SIZE: "100"
  INDEXER_INIT_INDEXES: "true"
  #INDEXER_INIT_INDEXES_ONLY: ${INDEXER_INIT_INDEXES_ONLY}
  INDEXER_LOG_MESSAGE: "false"
  #INDEXER_LOG_PAYLOAD: ${INDEXER_LOG_PAYLOAD}
  INDEXER_LOG_BATCH: "false"
  ELASTIC_APM_ENVIRONMENT: production
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  TRAIL_LEARNING_OBJECT_TYPE_ID: "d841e9d8-d669-4d88-9636-1072765d0738"
  MISSION_LEARNING_OBJECT_TYPE_ID: "798e50d7-8b97-4979-8728-4f9f1599bb05"
  INDEXER_SMARTZAP_GROUP_ID: "smartzap-data-sync-production"

  # Postgres
  PG_HOSTNAME: postgresrdsprod
  PG_PASSWORD: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  PG_PORT: "5432"
  PG_USER: postgres
  PG_DBNAME_KONQUEST: konquest_db
  PG_DBNAME_KONTENT: kontent_db
  PG_DBNAME_MYACCOUNT: myaccount_db
  PG_DBNAME_SMARTZAP: smartzap_db
  PG_DBNAME_NOTIFICATION: notification_db
  PG_DBNAME_REGULATORY_COMPLIANCE: regulatory_compliance_db
  PG_DBNAME_INTEGRATION_GATEWAY_ALURA: integration_gateway_alura_db
  PG_DBNAME_SISYPHUS: sisyphus_db

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: keeps-kafka-connector
  namespace: production
  labels:
    app: keeps-kafka-connector
spec:
  selector:
    matchLabels:
      app: keeps-kafka-connector
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: keeps-kafka-connector
      annotations:
        cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
    spec:
      volumes:
        - name: keeps-kafka-connector-secret-vol
          secret:
            secretName: keeps-kafka-connector-secret
      # tolerations:
      # - key: "nodeType"
      #   operator: "Equal"
      #   value: "connector"
      #   effect: "NoSchedule"
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #       - matchExpressions:
      #         - key: nodeType
      #           operator: In
      #           values:
      #           - connector
      containers:
        - name: keeps-kafka-connector
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/keeps-connector:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8083
          envFrom:
            - secretRef:
                name: keeps-kafka-connector-secret
                optional: false
          resources:
            requests:
              memory: 2Gi
              cpu: "1"
            limits:
              memory: 3.5Gi
              cpu: "1.5"
