apiVersion: v1
kind: Service
metadata:
  name: notification-svc
  namespace: production
  labels:
    app: notification
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: notification
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: notification-secret
  namespace: production
type: Opaque
stringData:
  HASH_SECRET_KEY: RVT5DJqpPiWGiQ6q # Remover após migração
  CRYPTO_SECRET_KEY: RVT5DJqpPiWGiQ6q
  NODE_ENV: production
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  AUTH_CLIENT_ID: keeps-notification-api
  AUTH_CLIENT_SECRET: juN5pkIpQuS9Frgv0hugK227Lyj0m8YF
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  #DB_USER: notification_user_db
  #DB_PASS: BwH0oEgHt82eUGUHydmqg5hGQd2U3qdKGeHTAoCjaRf3ZQVAvD
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_NAME: notification_db
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  DB_DIALECT: postgres
  AWS_SNS_TOPIC_ARN: arn:aws:sns:us-east-1:************:EmailMessageEvents
  APP_URL: https://learning-platform-api.keepsdev.com/notification
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.cloud.es.io
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_ENVIRONMENT: production
  TEMPLATES_PATH: "assets/email-templates"
  EMAIL_TEMPLATES_PATH: "assets/email-templates" #TODO: remove me
  EMAIL_TEMPLATES_PARTIALS_PATH: "assets/email-templates/partials"
  LOCALE_PATH: "assets/i18n"
  AWS_SES_SENDER: "Plataforma Aprendizagem <<EMAIL>>"
  AWS_SES_REGION: "us-east-1"
  AWS_SECRET_ACCESS_KEY: "kSiw9hpPegtd9D2EqdqVpGgNNn7ObqQF2haNBfaf"
  AWS_ACCESS_KEY_ID: "********************"
  I18N_PATH: "assets/i18n"
  MESSAGE_SENT_TRACK_TIMEOUT: "500"
  MIGRATIONS_RUN: "true"
  AUTH_APPLICATION_ID: "ad7e5ad2-1552-43ab-a471-710954f0e66a"
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  RABBITMQ_BELL_NOTIFICATIONS_QUEUE: bell
  BELL_NOTIFICATIONS_QUEUE_IS_DURABLE: "true"
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "637a2f9e72daba2ebb03a699c7a4c08d"
  BOT_SLACK: http://bot-slack-svc:3978/api/notification
  BOT_TEAMS: http://bot-teams-svc:3978/api/notification
  SERVICE_TWILIO_SENDER_PHONE: "+************"
  SERVICE_TWILIO_ACCOUNT_SID: "**********************************"
  SERVICE_TWILIO_TEMPLATES_PATH: assets/twilio-templates.json
  SERVICE_TWILIO_KEY_SID: "**********************************"
  SERVICE_TWILIO_KEY_SECRET: "3ce181845e6faa49685b7cdb81fe18a1"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification
  namespace: production
  labels:
    app: notification
spec:
  selector:
    matchLabels:
      app: notification
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: notification
    spec:
      volumes:
        - name: notification-secret-vol
          secret:
            secretName: notification-secret
      containers:
        - name: notification
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-notification:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: notification-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

