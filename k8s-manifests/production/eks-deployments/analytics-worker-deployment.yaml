apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-worker
  namespace: production
  labels:
    app: analytics-worker
spec:
  selector:
    matchLabels:
      app: analytics-worker
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: analytics-worker
    spec:
      volumes:
        - name: analytics-secret-vol
          secret:
            secretName: analytics-secret
      containers:
        - name: analytics-worker
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learn-analytics/learn-analytics-server:production"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: analytics-secret
                optional: false
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 3Gi
              cpu: "2"
          command:
            [
              "/usr/bin/supervisord",
              "-c",
              "/etc/supervisord.conf"
            ]