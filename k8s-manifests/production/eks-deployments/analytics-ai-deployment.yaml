apiVersion: v1
kind: Service
metadata:
  name: analytics-ai-svc
  namespace: production
  labels:
    app: analytics-ai
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: analytics-ai
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: analytics-ai-secret
  namespace: production
type: Opaque
stringData:
  ENVIRONMENT: production
  OPENAI_API_KEY: "************************************************************************************************************************************"
  DB_USER: postgres
  DB_PASSWORD: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_NAME: analytics_ai_db
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  KEYCLOAK_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  KEYCLOAK_ISS: https://iam.keepsdev.com/auth/realms/keeps
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt
  REPORTS_API_BASE_URL: https://learning-platform-api.keepsdev.com/analytics/api/v1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-ai
  namespace: production
  labels:
    app: analytics-ai
spec:
  selector:
    matchLabels:
      app: analytics-ai
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: analytics-ai
    spec:
      volumes:
        - name: analytics-ai-secret-vol
          secret:
            secretName: analytics-secret
      containers:
        - name: analytics-ai
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learn-analytics-ai:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: analytics-ai-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1
