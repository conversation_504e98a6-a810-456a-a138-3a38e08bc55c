apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: production
  labels:
    app: rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
      annotations:
        cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
    spec:
      containers:
        - name: rabbitmq
          image: rabbitmq:management-alpine
          volumeMounts:
            - name: rabbitmq-data
              mountPath: /var/lib/rabbitmq
          ports:
            - containerPort: 25672
            - containerPort: 5672
            - containerPort: 4369
            - containerPort: 15672
          env:
            - name: RABBITMQ_DEFAULT_USER
              value: user
            - name: RABBITMQ_DEFAULT_PASS
              value: ptxom2d18ZLt
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m
      volumes:
        - name: rabbitmq-data
          persistentVolumeClaim:
            claimName: rabbitmq-data-pvc
            
---
kind: Service
apiVersion: v1
metadata:
  name: rabbitmq
  namespace: production
spec:
  selector:
    app: rabbitmq
  ports:
    - name: p1
      protocol: TCP
      port: 25672
      targetPort: 25672
    - name: p2
      protocol: TCP
      port: 5672
      targetPort: 5672
    - name: p3
      protocol: TCP
      port: 4369
      targetPort: 4369
    - name: p4
      protocol: TCP
      port: 15672
      targetPort: 15672
