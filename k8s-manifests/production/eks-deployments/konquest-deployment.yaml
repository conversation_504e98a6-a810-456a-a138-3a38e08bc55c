apiVersion: v1
kind: Service
metadata:
  name: konquest-svc
  namespace: production
  labels:
    app: konquest
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: konquest
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: konquest-secret
  namespace: production
type: Opaque
stringData:
  DEBUG: "False"
  LOG_LEVEL: INFO
  ANALYTICS_API_URL: http://analytics-svc:8000/api/v1
  AWS_BUCKET_NAME: keeps-media
  AWS_CDN_BASE_URL: https://media.keepsdev.com
  AWS_COMPREHEND_ACCESS_KEY_ID: ********************
  AWS_COMPREHEND_SECRET_ACCESS_KEY: Mdet4eD0/ru7+DGAsqacJx/NjEp8qmf8H4FBWj75
  AWS_LAMBDA_ACCESS_KEY_ID: ********************
  AWS_LAMBDA_REGION_NAME: us-east-1
  AWS_LAMBDA_SECRET_ACCESS_KEY: RhLUlAQ+rDbAOkaXIG3iGfiLQ4zh0TIGiY/z402z
  AWS_MAIL_SENDER: Plataforma Aprendizagem <<EMAIL>>
  BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_NAMESPACE: CELERY
  CELERY_QUEUE: konquest
  CELERY_RESULT_BACKEND: celery_amqp_backend.AMQPBackend://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_USER_INTEGRATION_NUM_PROCS: "2"
  CERTIFICATE_PASSWORD: Vitoria100
  DATABASE_HOST: pgbouncer-svc
  DATABASE_NAME: konquest_db
  DATABASE_PASSWORD: 14582779f84f9110c23a60b17c9a6cd3
  DATABASE_PORT: "5432"
  DATABASE_USER: postgres
  DISABLE_EXPIRED_LEARNING_TRAILS_CRONTAB: "*/5 * * * *"
  DISCORD_WEBHOOK: https://discord.com/api/webhooks/1020763629181747230/e7mwR40Qpau0mWcvW0asH_JeTFtK8-yXSdQWx7MlhBhwSvaHS_sSP-FGp8skYJuz5tRP
  ELASTIC_APM_ENVIRONMENT: production
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.found.io:443
  ENVIRONMENT: production
  KEYCLOAK_CLIENT_ID: konquest
  KEYCLOAK_CLIENT_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  KEYCLOAK_CLIENT_SECRET_KEY: d6854af4-af6b-4e0c-af6b-32a826b0b2d9
  KEYCLOAK_REALM: keeps
  KONQUEST_CERTIFICATES_WEB_URL: https://konquest.keepsdev.com/settings/certificates
  KONQUEST_WEB_URL: https://konquest.keepsdev.com
  KONTENT_API_URL: http://kontent-svc:8000
  MYACCOUNT_API_URL: http://myaccount-svc:8000
  MYACCOUNT_V2_API_URL: http://myaccount-v2-svc:3000
  QUEUE_KONQUEST_COMPANY: konquest-companies
  QUEUE_KONQUEST_USER: konquest-users
  SLACK_LOG_CHANNEL_WEBHOOK: *****************************************************************************
  REDIS_LOCATION: redis://redis-svc:6379
  REDIS_PASSWORD: ptxom2d18ZLt
  USER_INFO_TIMEOUT: "1800"
  NOTIFICATION_API_URL: https://learning-platform-api.keepsdev.com/notification
  FORCE_HTTPS_IN_FORWARDED_HOST: "true"
  X_FORWARDED_HOST: learning-platform-api.keepsdev.com/konquest
  KONQUEST_WEB_MISSION_DETAIL_URL: https://konquest.keepsdev.com/C/{}
  REGULATORY_COMPLIANCE_QUEUE: regulatory-compliance-tasks
  REGULATORY_COMPLIANCE_EXCHANGE: integrations
  REGULATORY_COMPLIANCE_EXCHANGE_TYPE: direct
  WEB_CONCURRENCY: "3"
  WORKER_CONCURRENCY: "2"
  DATABASE_CONN_MAX_AGE: "30"
  CELERY_BEAT_LOG_LEVEL: DEBUG

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: konquest
  namespace: production
  labels:
    app: konquest
spec:
  selector:
    matchLabels:
      app: konquest
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: konquest
    spec:
      volumes:
        - name: konquest-secret-vol
          secret:
            secretName: konquest-secret
      containers:
        - name: konquest
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/konquest/konquest-server:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: konquest-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 250m
            limits:
              memory: 1Gi
              cpu: "1"
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "config.wsgi",
              "--bind",
              "0.0.0.0:8000",
            ]
