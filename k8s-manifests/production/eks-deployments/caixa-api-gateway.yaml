apiVersion: v1
kind: Service
metadata:
  name: caixa-api-gateway-svc
  namespace: production
  labels:
    app: caixa-api-gateway
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: caixa-api-gateway
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: caixa-api-gateway-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  CAIXA_API_URL: http://smartzap-caixa-svc:8000/caixa-api/v1
  NOTIFICATION_API_URL: http://notification-svc:3000
  ERROR_NOTIFICATION_RECEIVER_EMAIL: <EMAIL>
  AGENCIES_WORKSPACE_ID: df2d246e-62c7-448f-8dac-6c265f5f0081
  PARTNERS_WORKSPACE_ID: 4576514f-6630-4847-ad6b-04923c0f8556
  ELASTICSEARCH_SERVER: https://keeps.es.us-east-1.aws.found.io
  ELASTICSEARCH_USER: elastic
  ELASTICSEARCH_PASSWORD: Ztx2l2zLkf2DkmQYuAYRR0Ty
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: caixa-api-gateway
  namespace: production
  labels:
    app: caixa-api-gateway
spec:
  selector:
    matchLabels:
      app: caixa-api-gateway
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: caixa-api-gateway
    spec:
      volumes:
        - name: caixa-api-gateway-secret-vol
          secret:
            secretName: caixa-api-gateway-secret
      containers:
        - name: caixa-api-gateway
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/caixa-api-gateway:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: caixa-api-gateway-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

