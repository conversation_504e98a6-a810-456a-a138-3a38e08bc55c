apiVersion: v1
kind: Service
metadata:
  name: myaccount-svc
  namespace: production
  labels:
    app: myaccount
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: myaccount
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: myaccount-secret
  namespace: production
type: Opaque
stringData:
  DEBUG: "False"
  AWS_BUCKET_NAME: keeps-media
  AWS_BUCKET_PATH: myaccount
  AWS_BASE_CDN_URL: https://media.keepsdev.com
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  HASH_SECRET_KEY: "RVT5DJqpPiWGiQ6q"
  AWS_MAIL_SENDER: "Plataforma Aprendizagem <<EMAIL>>"
  BROKER_URL: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_BROKER_URL: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_QUEUE: myaccount
  CELERY_RESULT_BACKEND: "amqp://user:ptxom2d18ZLt@rabbitmq:5672"
  CELERY_RESULT_SERIALIZER: "json"
  CELERY_TIMEZONE: "America/Sao_Paulo"
  DATABASE_GAMEUP_NAME: "gameup_db"
  DATABASE_HOST: postgresrdsprod
  DATABASE_KEYCLOAK_NAME: keycloak_db
  DATABASE_KONQUEST_NAME: "konquest_db"
  DATABASE_NAME: "myaccount_db"
  DATABASE_PASSWORD: "5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP"
  DATABASE_PORT: "5432"
  DATABASE_SMARTZAP_NAME: "smartzap_db"
  DATABASE_USER: "postgres"
  DEBOUNCE_API_KEY: "60d49f73e0093"
  DEBOUNCE_API_URL: "https://api.debounce.io/v1/"
  DISCORD_WEBHOOK: https://discord.com/api/webhooks/1021831362015137912/lVVFj3rRLnXxHWn5U1hGwrU1TLNVePFGstbSozvpCitpih34dawsFlZzGx1lfsnUPBye
  ELASTIC_APM_ENVIRONMENT: production
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ENVIRONMENT: "production"
  IAM_ADMIN_PASS_ADMIN: "learnpl4tf0rm"
  IAM_ADMIN_REALM: "keeps"
  IAM_ADMIN_SERVER_URL: "http://keycloak-svc.security:8080/auth/"
  IAM_ADMIN_USER_ADMIN: "<EMAIL>"
  IAM_OPENID_CLIENT_ID: "myaccount"
  IAM_OPENID_PUBLIC_KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB"
  IAM_OPENID_REALM: "keeps"
  IAM_OPENID_SECRET_KEY: efe89a2a-07cc-436c-9d41-dc1fb72c8f6b
  IAM_OPENID_SERVER_URL: "http://keycloak-svc.security:8080/auth/"
  IAM_OPENID_TOKEN_URL: /protocol/openid-connect/token
  KEYCLOAK_CLIENT_ID: "myaccount"
  KEYCLOAK_CLIENT_PUBLIC_KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB"
  KEYCLOAK_CLIENT_SECRET_KEY: efe89a2a-07cc-436c-9d41-dc1fb72c8f6b
  KEYCLOAK_REALM: "keeps"
  KIZUP_API_URL: "https://kizup-api.keepsdev.com/api/1/"
  KIZUP_ID: "85d8e4b9-9582-4c98-926d-9322e40896db"
  KONQUEST_ID: "0abf08ea-d252-4d7c-ab45-ab3f9135c288"
  KONQUEST_WEB_URL: "https://konquest.keepsdev.com"
  MESSAGEBIRD_ACCESS_KEY: "*************************"
  MESSAGEBIRD_CHANNEL_ID: "442e50bac00848928b4a7f595ea6990c"
  MESSAGEBIRD_NAMESPACE: "6dadf5f1_35e6_4865_a6ff_be3535f8e460"
  MYACCOUNT_WEB_URL: "https://account.keepsdev.com"
  QUEUE_GAMEUP_COMPANY: gameup-companies
  QUEUE_GAMEUP_USER: gameup-users
  QUEUE_KONQUEST_COMPANY: konquest-companies
  QUEUE_KONQUEST_USER: konquest-users
  

  SLACK_LOG_CHANNEL_WEBHOOK: "*****************************************************************************"
  SMARTZAP_WEB_URL: "http://smartzap.keepsdev.com"
  TWILIO_ACCOUNT_SID: "**********************************"
  TWILIO_AUTH_TOKEN: "3ce181845e6faa49685b7cdb81fe18a1"
  TWILIO_SUPPORT_PHONE_NUMBER: "+************"
  WHATSAPP_BROKER: "twilio"
  NOTIFICATION_API_URL: https://learning-platform-api.keepsdev.com/notification
  FORCE_HTTPS_IN_FORWARDED_HOST: "true"
  X_FORWARDED_HOST: learning-platform-api.keepsdev.com/myaccount
  PYTHON_PATH: /usr/src/app/
  KAFKA_SERVERS: b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092
  WORKSPACE_TOPICS: '[{"workspace_id": "5c04ae80-21b2-4fe1-8afe-73a0b8c99d9d", "topic_key": "users_by_s3_csv_file_production.platformscience"},{"workspace_id": "15c24286-be9e-4bd0-801e-179c30031abd", "topic_key": "users_by_s3_csv_file_production.denso"},{"workspace_id": "ec53968c-6ff3-4964-9dfa-11276ecbf8c6", "topic_key": "users_by_s3_csv_file_production.centralit"},{"workspace_id": "55492a3c-6cc3-4423-8755-5776bab0bc9e", "topic_key": "users_by_s3_csv_file_production.universidade_edb"}, {"workspace_id": "df2d246e-62c7-448f-8dac-6c265f5f0081", "topic_key": "users_by_s3_csv_file_production.caixa", "role_id": "********-5e4e-48c6-91d7-dbeb360c7205"}]'
  KONQUEST_API_URL: http://konquest-svc:8000/
  KEEPS_SECRET_TOKEN_INTEGRATION: ********************************
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myaccount
  namespace: production
  labels:
    app: myaccount
spec:
  selector:
    matchLabels:
      app: myaccount
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: myaccount
    spec:
      volumes:
        - name: myaccount-secret-vol
          secret:
            secretName: myaccount-secret
      containers:
        - name: myaccount
          image: "************.dkr.ecr.us-east-1.amazonaws.com/account/my-account-server:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: myaccount-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "config.wsgi",
              "--bind",
              "0.0.0.0:8000",
            ]
