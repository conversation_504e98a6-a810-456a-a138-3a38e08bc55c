apiVersion: v1
kind: Service
metadata:
  name: corp-svc
  namespace: production
  labels:
    app: corp
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: corp
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: corp-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  KONQUEST_URL: http://konquest-svc:8000
  MYACCOUNT_URL: http://myaccount-svc:8000
  MYACCOUNT_V2_URL: http://myaccount-v2-svc:3000/api
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  AUTH_CLIENT_ID: keeps-corp-api
  AUTH_CLIENT_SECRET: o137sfrWKIN6l2kWDHcNMVFqS9J4cRUb
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: corp
  namespace: production
  labels:
    app: corp
spec:
  selector:
    matchLabels:
      app: corp
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: corp
    spec:
      volumes:
        - name: corp-secret-vol
          secret:
            secretName: corp-secret
      containers:
        - name: corp
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps-corp:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: corp-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 300m

