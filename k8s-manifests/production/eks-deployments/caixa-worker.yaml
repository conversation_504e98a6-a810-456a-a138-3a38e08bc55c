apiVersion: v1
kind: Service
metadata:
  name: caixa-worker-svc
  namespace: production
  labels:
    app: caixa-worker
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: caixa-worker
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: caixa-worker-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  ELASTICSEARCH_SERVER: https://keeps.es.us-east-1.aws.found.io
  ELASTICSEARCH_USER: elastic
  ELASTICSEARCH_PASSWORD: Ztx2l2zLkf2DkmQYuAYRR0Ty
  PARTNERS_FILE_PATH: assets/parceiros-caixa.xlsx
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  DB_NAME: caixa_partners
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_DIALECT: postgres
  DB_DEBUG: "false"
  MIGRATIONS_RUN: "true"

  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: caixa-worker
  namespace: production
  labels:
    app: caixa-worker
spec:
  selector:
    matchLabels:
      app: caixa-worker
  revisionHistoryLimit: 1
  replicas: 0
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: caixa-worker
    spec:
      volumes:
        - name: caixa-worker-secret-vol
          secret:
            secretName: caixa-worker-secret
      containers:
        - name: caixa-worker
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/caixa-worker:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: caixa-worker-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

