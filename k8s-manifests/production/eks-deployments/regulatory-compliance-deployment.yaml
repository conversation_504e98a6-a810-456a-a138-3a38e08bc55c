apiVersion: v1
kind: Service
metadata:
  name: regulatory-compliance-svc
  namespace: production
  labels:
    app: regulatory-compliance
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: regulatory-compliance
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: regulatory-compliance-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  AUTH_CLIENT_ID: keeps-regulatory-compliance-api
  AUTH_CLIENT_SECRET: qY92eBzgyPySgrOls8b5R3K9dTUxThT3
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  DB_HOST: "postgresrdsprod"
  DB_PORT: "5432"
  DB_USER: postgres
  DB_PASS: "5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP"
  DB_NAME: regulatory_compliance_db
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  KONQUEST_APPLICATION_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288  
  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  USER_INFO_TIMEOUT_MS: "600000"
  MIGRATIONS_RUN: "true"
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  RABBITMQ_QUEUE: regulatory-compliance-tasks
  RABBITMQ_QUEUE_IS_DURABLE: "true"
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "637a2f9e72daba2ebb03a699c7a4c08d"
  KONQUEST_URL: http://konquest-svc:8000
  KEEPS_SECRET_TOKEN_INTEGRATION: 637a2f9e72daba2ebb03a699c7a4c08d

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulatory-compliance
  namespace: production
  labels:
    app: regulatory-compliance
spec:
  selector:
    matchLabels:
      app: regulatory-compliance
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: regulatory-compliance
    spec:
      volumes:
        - name: regulatory-compliance-secret-vol
          secret:
            secretName: regulatory-compliance-secret
      containers:
        - name: regulatory-compliance
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-regulatory-compliance:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: regulatory-compliance-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

