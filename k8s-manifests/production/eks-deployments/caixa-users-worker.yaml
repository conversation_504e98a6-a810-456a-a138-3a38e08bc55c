apiVersion: v1
kind: Secret
metadata:
  name: caixa-users-worker-secret
  namespace: production
type: Opaque
stringData:
  AWS_ACCESS_KEY_ID: ********************
  AWS_BUCKET_PATH: df2d246e-62c7-448f-8dac-6c265f5f0081/users/utf8
  AWS_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  LOG_FILENAME: user_import.log
  S3_BUCKET_NAME: keeps-integrations
  SFTP_HOST: stsftpcorp.blob.core.windows.net
  SFTP_PASSWORD: zhXh0TzDyTBbepd7E+yPvkCNgHVfPmZr
  SFTP_USERNAME: stsftpcorp.azsftpescnegocios

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: caixa-users-worker-cronjob
  namespace: production
spec:
  schedule: "0 0 * * *"
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            app: caixa-users-worker
        spec:
          restartPolicy: Never
          volumes:
            - name: caixa-users-worker-secret-vol
              secret:
                secretName: caixa-users-worker-secret
          containers:
            - name: caixa-users-worker
              image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps/caixa-users-worker:production"
              imagePullPolicy: Always
              envFrom:
                - secretRef:
                    name: caixa-users-worker-secret
                    optional: false
              env:
                - name: PROCESSOR_CLASS
                  value: "CAIXA"
                - name: SFTP_REMOTE_PATH
                  value: base_funcionarios_caixa.csv
              resources:
                requests:
                  memory: "64Mi"
                  cpu: "100m"
                limits:
                  memory: "256Mi"
                  cpu: "250m"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: caixa-users-cvp-worker-cronjob
  namespace: production
spec:
  schedule: "0 1 * * 3"
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            app: caixa-users-cvp-worker
        spec:
          restartPolicy: Never
          volumes:
            - name: caixa-users-worker-secret-vol
              secret:
                secretName: caixa-users-worker-secret
          containers:
            - name: caixa-users-cvp-worker
              image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps/caixa-users-worker:production"
              imagePullPolicy: Always
              envFrom:
                - secretRef:
                    name: caixa-users-worker-secret
                    optional: false
              env:
                - name: PROCESSOR_CLASS
                  value: "CVP"
                - name: SFTP_REMOTE_PATH
                  value: base_funcionario_cvp.csv
                - name: CSV_ENCODING
                  value: utf-16
              resources:
                requests:
                  memory: "64Mi"
                  cpu: "100m"
                limits:
                  memory: "256Mi"
                  cpu: "250m"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: caixa-partners-worker-cronjob
  namespace: production
spec:
  schedule: "0 3 * * 3"
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            app: caixa-partners-worker
        spec:
          restartPolicy: Never
          volumes:
            - name: caixa-users-worker-secret-vol
              secret:
                secretName: caixa-users-worker-secret
          containers:
            - name: caixa-partners-worker
              image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/keeps/caixa-users-worker:production"
              imagePullPolicy: Always
              envFrom:
                - secretRef:
                    name: caixa-users-worker-secret
                    optional: false
              env:
                - name: PROCESSOR_CLASS
                  value: "PARTNERS"
                - name: SFTP_REMOTE_PATH
                  value: base_parceiros.csv
                - name: CSV_ENCODING
                  value: ISO-8859-1
                - name: AWS_BUCKET_PATH
                  value: df2d246e-62c7-448f-8dac-6c265f5f0081/partners/utf8
              resources:
                requests:
                  memory: "64Mi"
                  cpu: "100m"
                limits:
                  memory: "256Mi"
                  cpu: "250m"
