apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: production
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7.2.4
          ports:
            - containerPort: 6379
          args: ["redis-server", "--requirepass", "ptxom2d18ZLt"]

---
# Redis efêmero
kind: Service
apiVersion: v1
metadata:
  name: redis-svc
  namespace: production
spec:
  # type: LoadBalancer 
  selector:
    app: redis
  ports:
    - name: p1
      protocol: TCP
      port: 6379
      targetPort: 6379
