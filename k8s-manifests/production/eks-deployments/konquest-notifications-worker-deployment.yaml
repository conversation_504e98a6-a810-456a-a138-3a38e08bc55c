apiVersion: apps/v1
kind: Deployment
metadata:
  name: konquest-notification-worker
  namespace: production
  labels:
    app: konquest-notification-worker
spec:
  selector:
    matchLabels:
      app: konquest-notification-worker
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: konquest-notification-worker
    spec:
      volumes:
        - name: konquest-secret-vol
          secret:
            secretName: konquest-secret
      containers:
        - name: konquest-notification-worker
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/konquest/konquest-server:production"
          imagePullPolicy: Always

          envFrom:
            - secretRef:
                name: konquest-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m
          command:
            [
              "/usr/bin/supervisord",
              "-c",
              "/etc/supervisord_notifications_worker.conf"
            ]