apiVersion: v1
kind: Service
metadata:
  name: kontent-svc
  namespace: production
  labels:
    app: kontent
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8000
      targetPort: 8000
  selector:
    app: kontent
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: kontent-secret
  namespace: production
type: Opaque
stringData:
  DISCORD_WEBHOOK: https://discord.com/api/webhooks/1079823598744772688/b2EMx6VMz0J6a4op5CdCxRs9N4hRkb1T0V3v-ajqrCEEfylGWdR0PRg6TaG8l97uD1R1
  DEBUG: "False"
  LOG_LEVEL: error
  AWS_ACCESS_KEY_ID: ********************
  AWS_BUCKET_NAME: keeps.kontent.media.prd
  AWS_COMPREHEND_ACCESS_KEY_ID: ********************
  AWS_COMPREHEND_SECRET_ACCESS_KEY: Mdet4eD0/ru7+DGAsqacJx/NjEp8qmf8H4FBWj75
  AWS_REKOGNITION_ACCESS_KEY_ID: ********************
  AWS_REKOGNITION_SECRET_ACCESS_KEY: gwAejhbzwLadG+U7WoKni3fVLZ6TCHGYrW/LLN6o
  AWS_SECRET_ACCESS_KEY: TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf
  AWS_STREAMING_URL: https://contents.keepsdev.com
  AWS_TRANSCRIBE_ACCESS_KEY: ********************
  AWS_TRANSCRIBE_BUCKET: keeps.transcribe
  AWS_TRANSCRIBE_REGION: us-east-1
  AWS_TRANSCRIBE_SECRET_KEY: 4UOkYnWT7EQl7te3bovAxcfO1tMgHliM9tiyLPqo
  AWS_TRANSCRIBE_THRESHOLD: "0.5"
  AWS_TRANSCRIBE_VOCABULARY: keeps_pt_br
  BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_BROKER_URL: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_NAMESPACE: CELERY
  CELERY_QUEUE: kontent
  CELERY_RESULT_BACKEND: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  CELERY_RESULT_SERIALIZER: json
  CELERY_TASK_SERIALIZER: json
  CELERY_TIMEZONE: UTC
  CLOUD_CONVERT_KEY: FQ3vCTzlVViKxVlWvLh9S54ySoxFMIydJqNwy2aKZrg8ln7e2kZXY08xi5XAUWRs
  DATABASE_HOST: postgresrdsprod
  DATABASE_NAME: kontent_db
  DATABASE_PASSWORD: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DATABASE_PORT: "5432"
  DATABASE_USER: postgres
  ELASTICSEARCH_HOST: keeps.es.us-east-1.aws.found.io
  ELASTICSEARCH_INDEX: kontent-prod
  ELASTICSEARCH_PASS: Ztx2l2zLkf2DkmQYuAYRR0Ty
  ELASTICSEARCH_USER: elastic
  ELASTIC_APM_ENVIRONMENT: production
  ELASTIC_APM_SECRET_TOKEN: UGvFOr8Rghs1uKtXx8
  ELASTIC_APM_SERVER_URL: https://keeps.apm.us-east-1.aws.found.io:443
  ENVIRONMENT: production
  GOOGLE_APPLICATION_CREDENTIALS: /app/config/google_credentials/credential.json
  KEEPS_SECRET_TOKEN_INTEGRATION: 637a2f9e72daba2ebb03a699c7a4c08d
  KEYCLOAK_CLIENT_ID: kontent
  KEYCLOAK_CLIENT_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  KEYCLOAK_CLIENT_SECRET_KEY: f8d2e681-1484-4138-aea2-b3582e9e1094
  KEYCLOAK_REALM: keeps
  KONQUEST_API_URL: http://konquest-svc:8000/
  PDFTRON_KEY: demo:1656284562355:7a77bd2f0300000000d2365bcd5c173dcc5f8131ef6fffb2abba5498e0
  SLACK_LOG_CHANNEL_WEBHOOK: *****************************************************************************
  KONQUEST_CELERY_QUEUE: konquest
  VIMEO_TOKEN: 5640b8610f102d368d22aa7c16880764
  GOOGLE_YOUTUBE_API_KEY: AIzaSyDAgRdNdk08YBZxizeCwcWluVCWcbAXnLM
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kontent
  namespace: production
  labels:
    app: kontent
spec:
  selector:
    matchLabels:
      app: kontent
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: kontent
    spec:
      volumes:
        - name: kontent-secret-vol
          secret:
            secretName: kontent-secret
      containers:
        - name: kontent
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/kontent/kontent-server:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
          envFrom:
            - secretRef:
                name: kontent-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: "100m"
            limits:
              memory: 1Gi
              cpu: "1"
          args:
            [
              "gunicorn",
              "--access-logfile=-",
              "--config",
              "gunicorn_config.py",
              "config.wsgi",
              "--bind",
              "0.0.0.0:8000",
            ]
            