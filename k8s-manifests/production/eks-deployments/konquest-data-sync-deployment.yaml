apiVersion: apps/v1
kind: Deployment
metadata:
  name: konquest-data-sync
  namespace: production
  labels:
    app: konquest-data-sync
spec:
  selector:
    matchLabels:
      app: konquest-data-sync
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: konquest-data-sync
    spec:
      volumes:
        - name: keeps-kafka-connector-secret-vol
          secret:
            secretName: keeps-kafka-connector-secret
      containers:
        - name: konquest-data-sync
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/data-indexer:production"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: keeps-kafka-connector-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m
          command: ["python", "-u", "./app-konquest-data-sync.py"]
          