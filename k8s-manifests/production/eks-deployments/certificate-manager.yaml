apiVersion: v1
kind: Service
metadata:
  name: certificate-manager-svc
  namespace: production
  labels:
    app: certificate-manager
spec:
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000
    - name: grpc
      protocol: TCP
      port: 50051
      targetPort: 50051
  selector:
    app: certificate-manager
  type: ClusterIP

---
apiVersion: v1
kind: Secret
metadata:
  name: certificate-manager-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  I18N_PATH: assets/i18n
  TEMPLATES_PATH: assets/templates

  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  AUTH_CLIENT_ID: certificate-manager-api
  AUTH_CLIENT_SECRET: eG8hGLDV1tDY4uJJgCBQStTXfNb9YNq2
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_NAME: certificate_manager_db
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  DB_DIALECT: postgres

  MIGRATIONS_RUN: "true"
  KONQUEST_APPLICATION_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "637a2f9e72daba2ebb03a699c7a4c08d"

  AWS_S3_ACCESS_KEY_ID: ********************
  AWS_S3_SECRET_ACCESS_KEY: 4fef3QYmqshSGBFegnUiILnyor6WCRl+LxdLGCUq
  AWS_S3_REGION: "us-east-1"
  AWS_S3_BUCKET: keeps-media
  AWS_S3_BUCKET_PATH: certificate-manager
  AWS_S3_CDN_URL: https://media.keepsdev.com

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: certificate-manager
  namespace: production
  labels:
    app: certificate-manager
spec:
  selector:
    matchLabels:
      app: certificate-manager
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: certificate-manager
    spec:
      volumes:
        - name: certificate-manager-secret-vol
          secret:
            secretName: certificate-manager-secret
      containers:
        - name: certificate-manager
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-certificate-manager:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: certificate-manager-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1000m
          livenessProbe:
            tcpSocket:
              port: 50051
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: 50051
            initialDelaySeconds: 3
            periodSeconds: 5
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            allowPrivilegeEscalation: false

