apiVersion: v1
kind: Service
metadata:
  name: konquest-grpc-svc
  namespace: production
  labels:
    app: konquest-grpc
spec:
  selector:
    app: konquest-grpc
  ports:
    - name: grpc
      protocol: TCP
      port: 50051
      targetPort: 50051
  type: ClusterIP

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: konquest-grpc
  namespace: production
  labels:
    app: konquest-grpc
spec:
  replicas: 1
  revisionHistoryLimit: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: konquest-grpc
  template:
    metadata:
      labels:
        app: konquest-grpc
    spec:
      terminationGracePeriodSeconds: 10
      restartPolicy: Always
      volumes:
        - name: konquest-secret-vol
          secret:
            secretName: konquest-secret
      containers:
        - name: konquest-grpc
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/konquest/konquest-server:production"
          imagePullPolicy: Always
          ports:
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: konquest-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 512Mi
              cpu: 500m
          command: [
            "python",
            "manage.py",
            "run_grpc_server"
          ]
          livenessProbe:
            tcpSocket:
              port: 50051
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: 50051
            initialDelaySeconds: 3
            periodSeconds: 5
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            allowPrivilegeEscalation: false
