apiVersion: v1
kind: Service
metadata:
  name: pgbouncer-svc
  namespace: production
  labels:
    app: pgbouncer
spec:
  ports:
    - port: 5432
      targetPort: 5432
  selector:
    app: pgbouncer
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pgbouncer-config
  namespace: production
data:
  pgbouncer.ini: |
    [databases]
    konquest_db = host=postgresrdsprod port=5432 dbname=konquest_db user=postgres password=5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP

    [pgbouncer]
    listen_addr = 0.0.0.0
    listen_port = 5432
    auth_type = md5
    auth_file = /etc/pgbouncer/userlist.txt

    # Usa pooling no nível de transação
    pool_mode = transaction

    # Limite total de conexões aceitas pelo PgBouncer (max_db_connections * 3)
    max_client_conn = 1500  

    # Distribuição das conexões reais entre os bancos
    default_pool_size = 400
    reserve_pool_size = 100
    reserve_pool_timeout = 5.0

    # Limite total de conexões reais com o banco (Alinhado com o limite do RDS)
    max_db_connections = 500

    # Fechar conexões inativas após 60 segundos
    server_idle_timeout = 60.0  
    
    log_connections = 1
    log_disconnections = 1
  userlist.txt: |
    "postgres" "14582779f84f9110c23a60b17c9a6cd3"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer
  namespace: production
  labels:
    app: pgbouncer
spec:
  selector:
    matchLabels:
      app: pgbouncer
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: pgbouncer
    spec:
      volumes:
        - name: pgbouncer-config-vol
          configMap:
            name: pgbouncer-config
      containers:
        - name: pgbouncer
          image: edoburu/pgbouncer:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 5432
          volumeMounts:
            - name: pgbouncer-config-vol
              mountPath: /etc/pgbouncer
          
