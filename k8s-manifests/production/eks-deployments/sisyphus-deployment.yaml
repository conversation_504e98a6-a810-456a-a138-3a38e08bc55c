apiVersion: v1
kind: Service
metadata:
  name: sisyphus-svc
  namespace: production
  labels:
    app: sisyphus
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: sisyphus
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: sisyphus-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  DB_HOST: "postgresrdsprod"
  DB_PORT: "5432"
  DB_USER: postgres
  DB_PASS: "5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP"
  DB_NAME: sisyphus_db
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  KONQUEST_API_URL: http://konquest-svc:8000
  ANALYTICS_API_URL: http://analytics-svc:8000/api/v1
  KONQUEST_APPLICATION_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288  
  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  USER_INFO_TIMEOUT_MS: "600000"
  MIGRATIONS_RUN: "true"
  RABBITMQ_URI: amqp://user:ptxom2d18ZLt@rabbitmq:5672
  RABBITMQ_QUEUE: sisyphus-tasks
  RABBITMQ_ACTION_BATCH_PROCESSOR_QUEUE: sisyphus-action-process-tasks
  RABBITMQ_BATCH_PROCESSOR_QUEUE: sisyphus-batch-process-tasks
  RABBITMQ_QUEUE_IS_DURABLE: "true"
  OLD_MYACCOUNT_API_URL: "http://myaccount-svc:8000"
  MYACCOUNT_SUPER_TOKEN: "637a2f9e72daba2ebb03a699c7a4c08d"
  KEEPS_SECRET_TOKEN_INTEGRATION: 637a2f9e72daba2ebb03a699c7a4c08d
  AWS_S3_ACCESS_KEY_ID: ********************
  AWS_S3_SECRET_ACCESS_KEY: TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf
  AWS_S3_REGION: "us-east-1"
  AWS_S3_BUCKET: keeps-media
  AWS_S3_BUCKET_PATH: sisyphus
  AWS_S3_CDN_URL: https://media.keepsdev.com
  KONQUEST_GRPC_SERVER_URL: konquest-grpc-svc:50051
  MYACCOUNT_GRPC_SERVER_URL: myaccount-v2-svc:50051
  MYACCOUNT_APPLICATION_ID: ad7e5ad2-1552-43ab-a471-710954f0e66a

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sisyphus
  namespace: production
  labels:
    app: sisyphus
spec:
  selector:
    matchLabels:
      app: sisyphus
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: sisyphus
    spec:
      volumes:
        - name: sisyphus-secret-vol
          secret:
            secretName: sisyphus-secret
      containers:
        - name: sisyphus
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-sisyphus:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: sisyphus-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

