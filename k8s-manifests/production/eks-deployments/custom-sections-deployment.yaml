apiVersion: v1
kind: Service
metadata:
  name: custom-sections-svc
  namespace: production
  labels:
    app: custom-sections
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  - name: grpc
    protocol: TCP
    port: 50051
    targetPort: 50051
  selector:
    app: custom-sections
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: custom-sections-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  AUTH_CLIENT_ID: custom-sections-microservice
  AUTH_CLIENT_SECRET: b9POJwNKaO3G1r96GpWu8g8UZ3t6J9Y9
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  AUTH_DEBUG: "false"
  DB_USER: postgres
  DB_PASS: 5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
  DB_NAME: custom-sections_db
  DB_HOST: postgresrdsprod
  DB_PORT: "5432"
  DB_DIALECT: postgres
  DB_DEBUG: "false"
  MIGRATIONS_RUN: "true"
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000
  KONQUEST_API_URL: http://konquest-svc:8000
  AUTH_APPLICATION_ID: ad7e5ad2-1552-43ab-a471-710954f0e66a
  REDIS_HOST: redis-svc
  REDIS_PASSWORD: ptxom2d18ZLt
  REDIS_PORT: "6379"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: custom-sections
  namespace: production
  labels:
    app: custom-sections
spec:
  selector:
    matchLabels:
      app: custom-sections
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: custom-sections
    spec:
      volumes:
        - name: custom-sections-secret-vol
          secret:
            secretName: custom-sections-secret
      containers:
        - name: custom-sections
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-custom-sections:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
            - name: grpc
              containerPort: 50051
          envFrom:
            - secretRef:
                name: custom-sections-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 200m

