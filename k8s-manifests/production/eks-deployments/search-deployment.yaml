apiVersion: v1
kind: Service
metadata:
  name: search-svc
  namespace: production
  labels:
    app: search
spec:
  ports:
  - name: http
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: search
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: search-secret
  namespace: production
type: Opaque
stringData:
  NODE_ENV: production
  ENV_SUFFIX: production

  AUTH_URL: https://iam.keepsdev.com/auth/
  AUTH_REALM: keeps
  AUTH_CLIENT_ID: keeps-search-api
  AUTH_CLIENT_SECRET: 0oIIZx9bh4vcQg2WevY91ioTdOh0OzUh
  AUTH_REALM_PUBLIC_KEY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB
  
  ELASTICSEARCH_SERVER: https://keeps.es.us-east-1.aws.found.io
  ELASTICSEARCH_USER: elastic
  ELASTICSEARCH_PASSWORD: Ztx2l2zLkf2DkmQYuAYRR0Ty

  REDIS_HOST: redis-svc
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ptxom2d18ZLt
  REDIS_TTL_MS: "10000"

  KONQUEST_ID: 0abf08ea-d252-4d7c-ab45-ab3f9135c288
  ANALYTICS_ID: c2928f23-a5a6-4f59-94a7-7e409cf1d4f4
  MYACCOUNT_SUPER_TOKEN: 637a2f9e72daba2ebb03a699c7a4c08d
  
  OLD_MYACCOUNT_API_URL: http://myaccount-svc:8000
  MYACCOUNT_URL: http://myaccount-svc:8000 # TODO: remover depois de atualizar o myaccount-v2
  MYACCOUNT_API_URL: http://myaccount-v2-svc:3000

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: search
  namespace: production
  labels:
    app: search
spec:
  selector:
    matchLabels:
      app: search
  revisionHistoryLimit: 1
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: search
    spec:
      volumes:
        - name: search-secret-vol
          secret:
            secretName: search-secret
      containers:
        - name: search
          image: "************.dkr.ecr.us-east-1.amazonaws.com/keeps-search:production"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: search-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 250m

