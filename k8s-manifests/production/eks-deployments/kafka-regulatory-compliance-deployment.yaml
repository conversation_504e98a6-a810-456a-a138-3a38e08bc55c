apiVersion: apps/v1
kind: Deployment
metadata:
  name: keeps-kafka-regulatory-compliance
  namespace: production
  labels:
    app: keeps-kafka-regulatory-compliance
spec:
  selector:
    matchLabels:
      app: keeps-kafka-regulatory-compliance
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: keeps-kafka-regulatory-compliance
    spec:
      volumes:
        - name: keeps-kafka-connector-secret-vol
          secret:
            secretName: keeps-kafka-connector-secret
      containers:
        - name: keeps-kafka-regulatory-compliance
          image: "503825601340.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/data-indexer:production"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: keeps-kafka-connector-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
            limits:
              memory: 128Mi
              cpu: 250m
          command: ["python"]
          args: ["-u", "./regulatory-compliance-app.py"]
