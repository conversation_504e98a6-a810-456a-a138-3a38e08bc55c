apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-myaccount-legacy
  namespace: production
  annotations:  
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"    
spec:
  ingressClassName: nginx
  rules:
    - host: myaccount-api.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: myaccount-svc
                port:
                  number: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-analytics-legacy
  namespace: production
  annotations:  
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"    
spec:
  ingressClassName: nginx
  rules:
    - host: analytics-api.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: analytics-svc
                port:
                  number: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-kontent-legacy
  namespace: production
  annotations:  
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"    
spec:
  ingressClassName: nginx
  rules:
    - host: kontent-api.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kontent-svc
                port:
                  number: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-smartzap-legacy
  namespace: production
  annotations:  
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"    
spec:
  ingressClassName: nginx
  rules:
    - host: smartzap-api.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: smartzap-svc
                port:
                  number: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-konquest-legacy
  namespace: production
  annotations:  
    nginx.ingress.kubernetes.io/proxy-body-size: 600m
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"    
spec:
  ingressClassName: nginx
  rules:
    - host: konquest-api.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: konquest-svc
                port:
                  number: 8000