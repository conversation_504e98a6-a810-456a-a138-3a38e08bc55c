# Define um storage class com o tipo gp3. Esse storage class é específico do EKS
# Talvez pensar na possibilidade de passar esse código para o terraform

apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: gp3
  # Define como padrão
provisioner: ebs.csi.aws.com
parameters:
  type: gp3
  iops: "3000"                  # Personalize conforme necessário
  throughput: "125"             # Personalize conforme necessário
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer

# kubectl get storageclass   
# kubectl get pvc -n stage   
# kubectl delete pvc rabbitmq-data-pvc -n stage 