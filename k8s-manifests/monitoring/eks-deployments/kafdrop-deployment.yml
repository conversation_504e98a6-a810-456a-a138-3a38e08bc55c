apiVersion: v1
kind: Service
metadata:
  name: kafdrop-svc
  namespace: monitoring
  labels:
    app: kafdrop
spec:
  ports:
    - name: http
      protocol: TCP
      port: 9000
      targetPort: 9000
  selector:
    app: kafdrop
  type: NodePort

---
apiVersion: v1
kind: Secret
metadata:
  name: kafdrop-secret
  namespace: monitoring
type: Opaque
stringData:
  #KAFKA_BROKERCONNECT: "b-1.kafka-learning-platfor.7h2qni.c8.kafka.us-east-1.amazonaws.com:9092,b-2.kafka-learning-platfor.7h2qni.c8.kafka.us-east-1.amazonaws.com:9092"
  KAFKA_BROKERCONNECT: b-1.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-3.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092,b-2.mskkeeps.5qch79.c22.kafka.us-east-1.amazonaws.com:9092
  SERVER_SERVLET_CONTEXTPATH: /
  # CMD_ARGS: --topic.deleteEnabled=false --topic.createEnabled=false
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafdrop
  namespace: monitoring
  labels:
    app: kafdrop
spec:
  selector:
    matchLabels:
      app: kafdrop
  revisionHistoryLimit: 1
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: kafdrop
    spec:
      volumes:
        - name: kafdrop-secret-vol
          secret:
            secretName: kafdrop-secret
      containers:
        - name: kafdrop
          image: "obsidiandynamics/kafdrop:4.0.1"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 9000
          envFrom:
            - secretRef:
                name: kafdrop-secret
                optional: false
          resources:
            requests:
              memory: 64Mi
              cpu: 100m
