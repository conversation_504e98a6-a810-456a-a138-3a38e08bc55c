apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-connectors-production
  namespace: production
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
spec:
  ingressClassName: nginx
  rules:
    - host: connectors.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: keeps-kafka-connector-svc
                port:
                  number: 8083
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-connectors-stage
  namespace: stage
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
spec:
  ingressClassName: nginx
  rules:
    - host: connectors-stage.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: keeps-kafka-connector-svc
                port:
                  number: 8083
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-rabbitmq-production
  namespace: production
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  ingressClassName: nginx
  rules:
    - host: rabbitmq.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rabbitmq
                port:
                  number: 15672

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-rabbitmq-stage
  namespace: stage
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  ingressClassName: nginx
  rules:
    - host: rabbitmq-stage.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rabbitmq
                port:
                  number: 15672
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-grafana-dashboard
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  ingressClassName: nginx
  rules:
    - host: grafana.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: prometheus-grafana
                port:
                  number: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-kafdrop
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
spec:
  ingressClassName: nginx
  rules:
    - host: kafdrop.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kafdrop-svc
                port:
                  number: 9000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prometheus-ingress
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
spec:
  ingressClassName: nginx
  rules:
    - host: prometheus.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: prometheus-kube-prometheus-prometheus
                port:
                  number: 9090

---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-sonarqube
  namespace: qa
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "8m"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-redirect-from: "https://sonar.keepsdev.com"
    nginx.ingress.kubernetes.io/proxy-redirect-to: "http://sonar.keepsdev.com"
spec:
  ingressClassName: nginx
  rules:
    - host: sonar.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sonarqube-sonarqube
                port:
                  number: 9000