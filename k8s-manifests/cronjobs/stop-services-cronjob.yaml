apiVersion: batch/v1
kind: CronJob
metadata:
  name: stop-services
  namespace: stage
spec:
  schedule: "00 22 * * *" # Agendamento (UTC) +3
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: default
          containers:
          - name: stop-services
            image: bitnami/kubectl:latest
            command:
            - /bin/bash
            - -c
            - |
              echo "Parando serviços no namespace stage";
              kubectl scale deployment --all --replicas=0 -n stage
              kubectl scale statefulset sonarqube-sonarqube --replicas=0 -n qa 
          restartPolicy: OnFailure
