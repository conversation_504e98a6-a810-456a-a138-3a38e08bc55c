apiVersion: batch/v1
kind: CronJob
metadata:
  name: start-services
  namespace: stage
spec:
  schedule: "45 9 * * *" # Agendamento (UTC) +3
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: default
          containers:
          - name: start-services
            image: bitnami/kubectl:latest
            command:
            - /bin/bash
            - -c
            - |
              echo "Iniciando serviços no namespace stage";
              kubectl scale deployment --all --replicas=1 -n stage
              kubectl scale statefulset sonarqube-sonarqube --replicas=1 -n qa 
          restartPolicy: OnFailure

