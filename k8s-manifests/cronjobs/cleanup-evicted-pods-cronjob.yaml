apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cleanup-evicted-pods-role
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["list", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cleanup-evicted-pods-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cleanup-evicted-pods-role
subjects:
  - kind: ServiceAccount
    name: default
    namespace: kube-system
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cleanup-evicted-pods
  namespace: kube-system
spec:
  schedule: "* * * * *"  # every hour
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: cleanup
              image: bitnami/kubectl:latest
              command:
                - /bin/sh
                - -c
                - |
                  set -eu

                  echo "=== Apagando pods com Failed ==="
                  kubectl get pods -A --field-selector=status.phase=Failed -o name \
                    | xargs -r -n1 kubectl delete || true

                  echo "=== Apagando pods com Succeeded ==="
                  kubectl get pods -A --field-selector=status.phase=Succeeded -o name \
                    | xargs -r -n1 kubectl delete || true

                  echo "=== Apagando pods com Completed ==="
                  kubectl get pods -A --field-selector=status.phase=Completed -o name \
                    | xargs -r -n1 kubectl delete || true

                  echo "=== Apagando pods Evicted ou ContainerStatusUnknown ==="
                  kubectl get pods -A -o json \
                    | jq -r '.items[]
                        | select(
                            .status.reason == "Evicted" or
                            (.status.containerStatuses[]? | .state.waiting.reason == "ContainerStatusUnknown")
                          )
                        | "\(.metadata.namespace) \(.metadata.name)"' \
                    | while read ns pod; do
                        kubectl delete pod -n "$ns" "$pod" || true
                      done
          restartPolicy: OnFailure
          serviceAccountName: default
