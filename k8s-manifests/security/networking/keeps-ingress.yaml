apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: keycloak
  namespace: security
  annotations: 
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "256k"      
    nginx.ingress.kubernetes.io/server-snippet: |
      location ~ ^/auth/realms/.*/\.well-known/ {
          if ($http_x_keycloak_access != "keeps-frontend") {
              return 403;
          }
      }
spec:
  ingressClassName: nginx
  rules:
    - host: iam.keepsdev.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: keycloak-svc
                port:
                  number: 8080