apiVersion: v1
kind: Secret
metadata:
  name: keycloak-secret
  namespace: security
type: Opaque
stringData:
  KC_CACHE_CONFIG_FILE: "cache-ispn-jdbc-ping.xml"
  KC_DB_USERNAME: postgres
  KC_DB: postgres
  KC_DB_URL: "******************************************************"
  KEYCLOAK_ADMIN: "admin"
  KEYCLOAK_ADMIN_PASSWORD: "admin"
  KC_PROXY: "edge"
  KC_HTTP_RELATIVE_PATH: "/auth"
  KC_HOSTNAME_URL: "https://iam.keepsdev.com/auth"
  KC_HOSTNAME_ADMIN_URL: "https://iam.keepsdev.com/auth"
  KC_HOSTNAME: "https://iam.keepsdev.com/auth"
  KC_PROXY_HEADERS: "xforwarded"
  connectTimeout: "600000"
  remoteTimeout: "600000"
  USER_REGISTRATION_ENDPOINT_STAGE: "http://user-registration-svc.stage:3000/v1/user-registration"
  USER_REGISTRATION_ENDPOINT_PROD: "http://user-registration-svc.production:3000/v1/user-registration"
  USER_REGISTRATION_LOG_EVENTS: "false"
