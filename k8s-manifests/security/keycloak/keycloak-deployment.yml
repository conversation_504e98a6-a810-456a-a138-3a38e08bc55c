apiVersion: apps/v1
kind: Deployment
metadata:
  name: keycloak
  namespace: security
  labels:
    app: keycloak
spec:
  selector:
    matchLabels:
      app: keycloak
  revisionHistoryLimit: 1
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: keycloak
    spec:
      volumes:
        - name: postgres-secret-vol
          secret:
            secretName: postgres-secret
        - name: keycloak-secret-vol
          secret:
            secretName: keycloak-secret
        - name: vars-mapping
          configMap:
            name: keycloak-conf
            items:
              - key: keycloak.conf
                path: keycloak.conf
        - name: jgroups-mapping
          configMap:
            name: cache-ispn-jdbc-ping
            items:
              - key: cache-ispn-jdbc-ping.xml
                path: cache-ispn-jdbc-ping.xml
      containers:
        - name: keycloak
          image: '************.dkr.ecr.us-east-1.amazonaws.com/account/keycloak:latest'
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          env:
            - name: KC_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: password
          envFrom:
            - secretRef:
                name: keycloak-secret
                optional: false
          volumeMounts:
            - name: vars-mapping
              mountPath: /opt/keycloak/conf/keycloak.conf
              subPath: keycloak.conf
            - name: jgroups-mapping
              mountPath: /opt/keycloak/conf/cache-ispn-jdbc-ping.xml
              subPath: cache-ispn-jdbc-ping.xml
          resources:
            requests:
              cpu: "250m"
              memory: "512Mi"
            limits:
              cpu: "1000m"
              memory: "1Gi"
#          livenessProbe:
#            httpGet:
#              path: /auth
#              port: http
#          readinessProbe:
#            httpGet:
#              path: /auth/realms/master
#              port: http
#            initialDelaySeconds: 20
#            periodSeconds: 5
