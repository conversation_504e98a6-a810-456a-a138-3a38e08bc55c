# keeps-eks
Recursos para a terraformacao do cluster eks alem dos deployments dos micro servicoes e recursos de infra
https://docs.aws.amazon.com/eks/latest/userguide/setting-up.html

# Tips
If you get some error like: Kubernetes cluster unreachable: invalid configuration: no configuration has been provided, try setting KUBERNETES_MASTER environment variable
execute `export KUBE_CONFIG_PATH=~/.kube/config`


## Hosts
- storybook.keepsdev.com                   
- sonar.keepsdev.com                       
- smartzap.keepsdev.com                    
- smartzap-stage.keepsdev.com              
- smartzap-api.keepsdev.com                
- smartview.keepsdev.com                   
- smartview-stage.keepsdev.com             
- rabbitmq.keepsdev.com                    
- rabbitmq-stage.keepsdev.com              
- myaccount-stage.keepsdev.com             
- myaccount-api.keepsdev.com               
- learning-platform-api.keepsdev.com       
- learning-platform-api-stage.keepsdev.com 
- kontent-api.keepsdev.com                 
- konquest.keepsdev.com                    
- konquest-stage.keepsdev.com              
- konquest-api.keepsdev.com                
- kizup-api.keepsdev.com	
- kafdrop.keepsdev.com                     
- iam.keepsdev.com                         
- grafana.keepsdev.com                     
- docs.keepsdev.com                        
- contents.keepsdev.com                    
- contents-stage.keepsdev.com              
- connectors.keepsdev.com                  
- connectors-stage.keepsdev.com            
- assets.keepsdev.com                      
- analytics.keepsdev.com                   
- analytics-stage.keepsdev.com             
- analytics-api.keepsdev.com               
- account.keepsdev.com  - (Remover) 
- myaccount.keepsdev.com                  

### SSO ferramentas
 - https://medium.com/@charled.breteche/securing-grafana-with-keycloak-sso-d01fec05d984
 - https://keycloakthemes.com/blog/how-to-setup-sign-in-with-google-using-keycloak
 - https://docs.sonarqube.org/9.7/instance-administration/authentication/saml/how-to-set-up-keycloak/


 ### **Problema: Pods Pending Após Atualização do EKS**

Após a atualização do cluster EKS para a última versão, alguns pods ficaram no estado **`Pending`**. A análise dos eventos do pod revelou a seguinte mensagem:

```plaintext
Warning  FailedScheduling  0/20 nodes are available: 20 node(s) didn't match Pod's node affinity/selector.
```

---

### **Causa**
O problema foi causado pela falta de labels e taints necessários nos nós para que os pods pudessem ser agendados. Isso ocorreu porque a atualização do EKS removeu ou resetou essas configurações nos nós.

---

### **Solução**

Para resolver o problema, foi necessário adicionar novamente os labels e taints aos nós que deveriam hospedar os pods do **Connector**. Seguem os passos realizados:

---

#### **1. Verificar os Labels e Taints Atuais**
1. **Verificar os Labels nos Nós**:
   ```bash
   kubectl get nodes --show-labels
   ```

2. **Verificar os Taints nos Nós**:
   ```bash
   kubectl describe nodes | grep Taints
   ```

---

#### **2. Adicionar Labels e Taints**
Os seguintes comandos foram usados para adicionar os labels e taints necessários:

1. **Adicionar Label ao Nó**:
   ```bash
   kubectl label node <nome-do-no> nodeType=connector --overwrite
   ```

2. **Adicionar Taint ao Nó**:
   ```bash
   kubectl taint nodes <nome-do-no> nodeType=connector:NoSchedule --overwrite
   ```

---

#### **3. Verificar os Pods**
Após aplicar os labels e taints, o pod foi automaticamente agendado no nó correto. Verificamos o status do pod com:

```bash
kubectl get pods -n <namespace>
```